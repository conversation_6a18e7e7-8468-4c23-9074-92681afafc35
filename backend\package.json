{"name": "kuber-ai-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["trading", "algorithmic", "backtesting", "finance"], "author": "", "license": "ISC", "description": "Backend API for Kuber.ai trading platform", "dependencies": {"@prisma/client": "^6.9.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^4.1.0", "prisma": "^6.9.0", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "ws": "^8.18.2", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "@types/node-cron": "^3.0.11", "@types/ws": "^8.18.1", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}