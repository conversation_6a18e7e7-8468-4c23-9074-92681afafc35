// Kuber.ai Trading Platform Database Schema
// This schema defines the database structure for trading strategies,
// market data, portfolio management, and backtesting

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model for future authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  portfolios Portfolio[]
  strategies Strategy[]
  trades     Trade[]
  backtests  BacktestResult[]

  @@map("users")
}

// Trading Strategy model
model Strategy {
  id          String        @id @default(cuid())
  name        String
  description String
  asset       String
  status      StrategyStatus @default(STOPPED)
  pnl         Float         @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Strategy parameters stored as JSON
  parameters Json

  // Relations
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades    Trade[]
  backtests BacktestResult[]

  @@map("strategies")
}

// Market Data model for historical prices
model MarketData {
  id            String   @id @default(cuid())
  symbol        String
  timestamp     DateTime
  open          Float
  high          Float
  low           Float
  close         Float
  volume        Int
  adjustedClose Float?
  createdAt     DateTime @default(now())

  // Composite index for efficient querying
  @@unique([symbol, timestamp])
  @@index([symbol, timestamp])
  @@map("market_data")
}

// Portfolio model
model Portfolio {
  id        String   @id @default(cuid())
  name      String   @default("Default Portfolio")
  totalValue Float   @default(0)
  cash      Float    @default(0)
  dailyPnL  Float    @default(0)
  totalPnL  Float    @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId   String
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  holdings Holding[]

  @@map("portfolios")
}

// Holdings within a portfolio
model Holding {
  id                   String   @id @default(cuid())
  symbol               String
  quantity             Float
  averagePrice         Float
  currentPrice         Float    @default(0)
  marketValue          Float    @default(0)
  unrealizedPnL        Float    @default(0)
  unrealizedPnLPercent Float    @default(0)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  portfolioId String
  portfolio   Portfolio @relation(fields: [portfolioId], references: [id], onDelete: Cascade)

  @@unique([portfolioId, symbol])
  @@map("holdings")
}

// Trade execution model
model Trade {
  id        String      @id @default(cuid())
  symbol    String
  type      TradeType
  quantity  Float
  price     Float
  fees      Float       @default(0)
  timestamp DateTime    @default(now())
  status    TradeStatus @default(PENDING)
  reason    String?     // Why the trade was executed

  // Relations
  userId     String
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  strategyId String?
  strategy   Strategy? @relation(fields: [strategyId], references: [id], onDelete: SetNull)

  @@map("trades")
}

// Backtest results model
model BacktestResult {
  id                   String   @id @default(cuid())
  symbol               String
  startDate            DateTime
  endDate              DateTime
  initialCapital       Float
  finalCapital         Float
  totalReturn          Float
  totalReturnPercent   Float
  maxDrawdown          Float
  maxDrawdownPercent   Float
  sharpeRatio          Float?
  winRate              Float
  totalTrades          Int
  winningTrades        Int
  losingTrades         Int
  averageWin           Float
  averageLoss          Float
  profitFactor         Float
  createdAt            DateTime @default(now())

  // Store trades and equity curve as JSON for flexibility
  trades      Json // Array of BacktestTrade objects
  equityCurve Json // Array of EquityPoint objects
  parameters  Json // Strategy parameters used

  // Relations
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  strategyId String
  strategy   Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@map("backtest_results")
}

// Enums
enum StrategyStatus {
  ACTIVE
  PAUSED
  STOPPED
}

enum TradeType {
  BUY
  SELL
}

enum TradeStatus {
  PENDING
  EXECUTED
  CANCELLED
  FAILED
}
