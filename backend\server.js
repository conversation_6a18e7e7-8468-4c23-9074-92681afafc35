const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:5173",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Kuber.ai API v1.0',
    version: '1.0.0',
    endpoints: {
      strategies: '/api/strategies',
      marketData: '/api/market-data',
      portfolio: '/api/portfolio',
      backtest: '/api/backtest',
      nlpStrategy: '/api/nlp-strategy'
    }
  });
});

// Mock NLP Strategy endpoint for testing
app.post('/api/nlp-strategy/parse', (req, res) => {
  const { naturalLanguage } = req.body;
  
  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  // Mock parsed strategy
  const mockStrategy = {
    name: 'MACD Strategy - RELIANCE',
    description: `Auto-generated strategy: ${naturalLanguage}`,
    asset: 'RELIANCE',
    parameters: {
      entryConditions: [
        {
          type: 'MACD_CROSS',
          parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
        }
      ],
      exitConditions: [
        { type: 'STOP_LOSS', parameters: { percentage: 5 } },
        { type: 'TAKE_PROFIT', parameters: { percentage: 15 } }
      ],
      riskManagement: {
        stopLossPercentage: 5,
        takeProfitPercentage: 15,
        maxDrawdownPercentage: 10,
        positionSizing: 'PERCENTAGE'
      },
      timeframe: '1d',
      maxPositionSize: 10000
    }
  };

  res.json({
    success: true,
    data: {
      strategy: mockStrategy,
      validation: {
        isValid: true,
        errors: []
      }
    },
    message: 'Strategy parsed successfully'
  });
});

// Mock strategy examples endpoint
app.get('/api/nlp-strategy/examples', (req, res) => {
  const examples = [
    {
      category: 'MACD Strategies',
      examples: [
        'Buy RELIANCE when MACD crosses above signal line, stop loss at 5%, take profit at 15%',
        'Buy TCS when MACD histogram turns positive, hold for 30 days maximum',
        'Buy INFY when MACD crosses above signal with volume 2x average, stop loss 3%'
      ]
    },
    {
      category: 'RSI Strategies',
      examples: [
        'Buy HDFC when RSI drops below 30, sell when RSI goes above 70',
        'Buy TATAMOTORS when RSI below 25, stop loss 4%, take profit 12%',
        'Buy ICICIBANK when RSI oversold below 30, hold for 15 days'
      ]
    },
    {
      category: 'Breakout Strategies',
      examples: [
        'Buy BHARTIARTL on volume breakout above resistance with 3x volume, stop loss 6%',
        'Buy WIPRO when price breaks above 20-day moving average, take profit 10%',
        'Buy MARUTI on price breakout with volume spike, stop loss 5%, target 20%'
      ]
    }
  ];

  res.json({
    success: true,
    data: examples,
    message: 'Example strategy descriptions'
  });
});

// Mock quick test endpoint
app.post('/api/nlp-strategy/quick-test', (req, res) => {
  const { naturalLanguage } = req.body;
  
  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  const mockStrategy = {
    name: 'Test Strategy',
    description: naturalLanguage,
    asset: 'RELIANCE',
    parameters: {
      entryConditions: [{ type: 'MACD_CROSS', parameters: {} }],
      exitConditions: [{ type: 'STOP_LOSS', parameters: { percentage: 5 } }],
      riskManagement: { stopLossPercentage: 5, takeProfitPercentage: 15 },
      timeframe: '1d',
      maxPositionSize: 10000
    }
  };

  const codeRepresentation = `
// Generated Trading Strategy: ${mockStrategy.name}
const strategy = {
  name: "${mockStrategy.name}",
  asset: "${mockStrategy.asset}",
  timeframe: "${mockStrategy.parameters.timeframe}",
  entryConditions: ${JSON.stringify(mockStrategy.parameters.entryConditions, null, 2)},
  exitConditions: ${JSON.stringify(mockStrategy.parameters.exitConditions, null, 2)}
};
`.trim();

  res.json({
    success: true,
    data: {
      originalText: naturalLanguage,
      parsedStrategy: mockStrategy,
      validation: { isValid: true, errors: [] },
      codeRepresentation
    },
    message: 'Strategy parsing test completed'
  });
});

// Mock create and backtest endpoint
app.post('/api/nlp-strategy/create-and-backtest', (req, res) => {
  const { naturalLanguage, backtestPeriod = '1yr', initialCapital = 100000 } = req.body;
  
  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  // Mock strategy and backtest result
  const mockStrategy = {
    id: 'strategy-' + Date.now(),
    name: 'Auto-Generated Strategy',
    description: naturalLanguage,
    asset: 'RELIANCE',
    status: 'STOPPED',
    pnl: 0
  };

  const mockBacktestResult = {
    id: 'backtest-' + Date.now(),
    strategyId: mockStrategy.id,
    symbol: 'RELIANCE',
    startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
    initialCapital,
    finalCapital: initialCapital * 1.15, // 15% return
    totalReturn: initialCapital * 0.15,
    totalReturnPercent: 15,
    maxDrawdown: initialCapital * 0.05,
    maxDrawdownPercent: 5,
    sharpeRatio: 1.2,
    winRate: 65,
    totalTrades: 24,
    winningTrades: 16,
    losingTrades: 8,
    averageWin: 2500,
    averageLoss: 1200,
    profitFactor: 2.1,
    trades: [],
    equity: [],
    createdAt: new Date()
  };

  res.json({
    success: true,
    data: {
      strategy: mockStrategy,
      backtestResult: mockBacktestResult,
      validation: { isValid: true, errors: [] }
    },
    message: `Strategy "${mockStrategy.name}" created and backtested successfully`
  });
});

// Error handling middleware
app.use((req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
});

app.use((err, req, res, next) => {
  res.status(res.statusCode === 200 ? 500 : res.statusCode).json({
    success: false,
    error: err.message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
  console.log(`🤖 NLP Strategy API ready at http://localhost:${PORT}/api/nlp-strategy`);
});
