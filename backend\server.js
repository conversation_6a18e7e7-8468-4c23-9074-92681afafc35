const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:5173",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Kuber.ai API v1.0',
    version: '1.0.0',
    endpoints: {
      strategies: '/api/strategies',
      marketData: '/api/market-data',
      portfolio: '/api/portfolio',
      backtest: '/api/backtest',
      nlpStrategy: '/api/nlp-strategy'
    }
  });
});

// Mock NLP Strategy endpoint for testing
app.post('/api/nlp-strategy/parse', (req, res) => {
  const { naturalLanguage } = req.body;
  
  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  // Mock parsed strategy
  const mockStrategy = {
    name: 'MACD Strategy - RELIANCE',
    description: `Auto-generated strategy: ${naturalLanguage}`,
    asset: 'RELIANCE',
    parameters: {
      entryConditions: [
        {
          type: 'MACD_CROSS',
          parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
        }
      ],
      exitConditions: [
        { type: 'STOP_LOSS', parameters: { percentage: 5 } },
        { type: 'TAKE_PROFIT', parameters: { percentage: 15 } }
      ],
      riskManagement: {
        stopLossPercentage: 5,
        takeProfitPercentage: 15,
        maxDrawdownPercentage: 10,
        positionSizing: 'PERCENTAGE'
      },
      timeframe: '1d',
      maxPositionSize: 10000
    }
  };

  res.json({
    success: true,
    data: {
      strategy: mockStrategy,
      validation: {
        isValid: true,
        errors: []
      }
    },
    message: 'Strategy parsed successfully'
  });
});

// Mock strategy examples endpoint
app.get('/api/nlp-strategy/examples', (req, res) => {
  const examples = [
    {
      category: 'MACD Strategies',
      examples: [
        'Buy RELIANCE when MACD crosses above signal line, stop loss at 5%, take profit at 15%',
        'Buy TCS when MACD histogram turns positive, hold for 30 days maximum',
        'Buy INFY when MACD crosses above signal with volume 2x average, stop loss 3%'
      ]
    },
    {
      category: 'RSI Strategies',
      examples: [
        'Buy HDFC when RSI drops below 30, sell when RSI goes above 70',
        'Buy TATAMOTORS when RSI below 25, stop loss 4%, take profit 12%',
        'Buy ICICIBANK when RSI oversold below 30, hold for 15 days'
      ]
    },
    {
      category: 'Breakout Strategies',
      examples: [
        'Buy BHARTIARTL on volume breakout above resistance with 3x volume, stop loss 6%',
        'Buy WIPRO when price breaks above 20-day moving average, take profit 10%',
        'Buy MARUTI on price breakout with volume spike, stop loss 5%, target 20%'
      ]
    }
  ];

  res.json({
    success: true,
    data: examples,
    message: 'Example strategy descriptions'
  });
});

// Mock quick test endpoint
app.post('/api/nlp-strategy/quick-test', (req, res) => {
  const { naturalLanguage } = req.body;
  
  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  const mockStrategy = {
    name: 'Test Strategy',
    description: naturalLanguage,
    asset: 'RELIANCE',
    parameters: {
      entryConditions: [{ type: 'MACD_CROSS', parameters: {} }],
      exitConditions: [{ type: 'STOP_LOSS', parameters: { percentage: 5 } }],
      riskManagement: { stopLossPercentage: 5, takeProfitPercentage: 15 },
      timeframe: '1d',
      maxPositionSize: 10000
    }
  };

  const codeRepresentation = `
// Generated Trading Strategy: ${mockStrategy.name}
const strategy = {
  name: "${mockStrategy.name}",
  asset: "${mockStrategy.asset}",
  timeframe: "${mockStrategy.parameters.timeframe}",
  entryConditions: ${JSON.stringify(mockStrategy.parameters.entryConditions, null, 2)},
  exitConditions: ${JSON.stringify(mockStrategy.parameters.exitConditions, null, 2)}
};
`.trim();

  res.json({
    success: true,
    data: {
      originalText: naturalLanguage,
      parsedStrategy: mockStrategy,
      validation: { isValid: true, errors: [] },
      codeRepresentation
    },
    message: 'Strategy parsing test completed'
  });
});

// Mock create and backtest endpoint with enhanced Indian market simulation
app.post('/api/nlp-strategy/create-and-backtest', (req, res) => {
  const { naturalLanguage, backtestPeriod = '1yr', initialCapital = 100000 } = req.body;

  if (!naturalLanguage) {
    return res.status(400).json({
      success: false,
      error: 'Natural language description is required'
    });
  }

  // Extract asset from natural language
  const extractAsset = (text) => {
    const indianStocks = {
      'reliance': 'RELIANCE',
      'tcs': 'TCS',
      'hdfc': 'HDFCBANK',
      'infy': 'INFY',
      'infosys': 'INFY',
      'icici': 'ICICIBANK',
      'sbi': 'SBIN',
      'bharti': 'BHARTIARTL',
      'airtel': 'BHARTIARTL',
      'itc': 'ITC',
      'wipro': 'WIPRO',
      'maruti': 'MARUTI',
      'bajaj': 'BAJFINANCE',
      'hcl': 'HCLTECH',
      'tata motors': 'TATAMOTORS',
      'tatamotors': 'TATAMOTORS'
    };

    const lowerText = text.toLowerCase();
    for (const [key, value] of Object.entries(indianStocks)) {
      if (lowerText.includes(key)) {
        return value;
      }
    }
    return 'RELIANCE'; // Default
  };

  const asset = extractAsset(naturalLanguage);

  // Mock strategy with parsed details
  const mockStrategy = {
    id: 'strategy-' + Date.now(),
    name: `AI Strategy - ${asset}`,
    description: naturalLanguage,
    asset: asset,
    status: 'STOPPED',
    pnl: 0,
    parameters: {
      entryConditions: [
        { type: 'MACD_CROSS', parameters: { fastPeriod: 12, slowPeriod: 26 } }
      ],
      exitConditions: [
        { type: 'STOP_LOSS', parameters: { percentage: 5 } },
        { type: 'TAKE_PROFIT', parameters: { percentage: 15 } }
      ],
      riskManagement: {
        stopLossPercentage: 5,
        takeProfitPercentage: 15,
        positionSizing: 'PERCENTAGE'
      },
      timeframe: '1d',
      maxPositionSize: 10000
    }
  };

  // Enhanced backtest result with realistic Indian market performance
  const baseReturn = Math.random() * 30 - 10; // -10% to +20% return
  const finalCapital = initialCapital * (1 + baseReturn / 100);
  const totalReturn = finalCapital - initialCapital;

  const mockBacktestResult = {
    id: 'backtest-' + Date.now(),
    strategyId: mockStrategy.id,
    symbol: asset,
    startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
    initialCapital,
    finalCapital: Math.round(finalCapital),
    totalReturn: Math.round(totalReturn),
    totalReturnPercent: Math.round(baseReturn * 100) / 100,
    maxDrawdown: Math.round(initialCapital * (0.03 + Math.random() * 0.07)), // 3-10% drawdown
    maxDrawdownPercent: Math.round((3 + Math.random() * 7) * 100) / 100,
    sharpeRatio: Math.round((0.5 + Math.random() * 1.5) * 100) / 100, // 0.5-2.0
    winRate: Math.round((45 + Math.random() * 30) * 100) / 100, // 45-75%
    totalTrades: Math.floor(15 + Math.random() * 25), // 15-40 trades
    winningTrades: 0, // Will be calculated
    losingTrades: 0, // Will be calculated
    averageWin: Math.round(1500 + Math.random() * 2000), // ₹1500-3500
    averageLoss: Math.round(800 + Math.random() * 1000), // ₹800-1800
    profitFactor: Math.round((1.2 + Math.random() * 1.8) * 100) / 100, // 1.2-3.0
    trades: [],
    equity: [],
    createdAt: new Date()
  };

  // Calculate winning/losing trades
  mockBacktestResult.winningTrades = Math.round(mockBacktestResult.totalTrades * mockBacktestResult.winRate / 100);
  mockBacktestResult.losingTrades = mockBacktestResult.totalTrades - mockBacktestResult.winningTrades;

  res.json({
    success: true,
    data: {
      strategy: mockStrategy,
      backtestResult: mockBacktestResult,
      validation: { isValid: true, errors: [] }
    },
    message: `Strategy "${mockStrategy.name}" created and backtested successfully with Indian market data`
  });
});

// API Configuration endpoint
app.get('/api/config', (req, res) => {
  res.json({
    success: true,
    data: {
      indianMarketAPI: {
        baseUrl: process.env.INDIAN_MARKET_API_URL || 'https://api.indianmarket.com',
        useRealAPI: process.env.USE_REAL_INDIAN_API === 'true',
        status: process.env.USE_REAL_INDIAN_API === 'true' ? 'Real API' : 'Mock Data'
      },
      features: {
        nlpStrategy: true,
        backtesting: true,
        realTimeData: true,
        indianStocks: true
      }
    },
    message: 'API configuration'
  });
});

// Test Indian Market API connection
app.get('/api/market-data/test-connection', async (req, res) => {
  try {
    const useRealAPI = process.env.USE_REAL_INDIAN_API === 'true';

    if (!useRealAPI) {
      return res.json({
        success: true,
        data: {
          mode: 'mock',
          message: 'Using mock data for development',
          instruction: 'Set USE_REAL_INDIAN_API=true in .env to use real API'
        }
      });
    }

    // Test real API connection
    const axios = require('axios');
    const apiUrl = process.env.INDIAN_MARKET_API_URL || 'https://stock.indianapi.in';

    try {
      const headers = {
        'Accept': 'application/json',
        'User-Agent': 'Kuber.ai/1.0'
      };

      if (process.env.INDIAN_MARKET_API_KEY) {
        headers['X-Api-Key'] = process.env.INDIAN_MARKET_API_KEY;
      }

      const response = await axios.get(`${apiUrl}/historical_data`, {
        params: {
          stock_name: 'RELIANCE',
          period: '1m',
          filter: 'default'
        },
        timeout: 10000,
        headers
      });

      res.json({
        success: true,
        data: {
          mode: 'real',
          status: 'Connected',
          apiUrl: apiUrl,
          responseStatus: response.status,
          datasetCount: response.data?.datasets?.length || 0,
          sampleData: response.data?.datasets?.[0]?.values?.slice(0, 2) || []
        },
        message: 'Successfully connected to Indian Market API'
      });

    } catch (apiError) {
      res.status(500).json({
        success: false,
        data: {
          mode: 'real',
          status: 'Failed',
          apiUrl: apiUrl,
          error: apiError.message,
          statusCode: apiError.response?.status
        },
        error: `Failed to connect to Indian Market API: ${apiError.message}`
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Indian Market Data endpoints
app.get('/api/market-data/search', (req, res) => {
  const { q } = req.query;

  if (!q) {
    return res.status(400).json({
      success: false,
      error: 'Query parameter "q" is required'
    });
  }

  const indianStocks = [
    { symbol: 'RELIANCE', name: 'Reliance Industries Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'TCS', name: 'Tata Consultancy Services Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'INFY', name: 'Infosys Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE', market: 'Indian' },
    { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'ITC', name: 'ITC Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'WIPRO', name: 'Wipro Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'HCLTECH', name: 'HCL Technologies Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', exchange: 'NSE', market: 'Indian' },
    { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Limited', exchange: 'NSE', market: 'Indian' }
  ];

  const results = indianStocks.filter(stock =>
    stock.symbol.toLowerCase().includes(q.toLowerCase()) ||
    stock.name.toLowerCase().includes(q.toLowerCase())
  );

  res.json({
    success: true,
    data: results,
    message: `Found ${results.length} Indian stocks matching "${q}"`
  });
});

app.get('/api/market-data/current/:symbol', (req, res) => {
  const { symbol } = req.params;

  // Mock current prices for Indian stocks
  const basePrices = {
    'RELIANCE': 2580,
    'TCS': 3420,
    'HDFCBANK': 1720,
    'INFY': 1465,
    'ICICIBANK': 945,
    'SBIN': 565,
    'BHARTIARTL': 825,
    'ITC': 465,
    'WIPRO': 415,
    'MARUTI': 9850,
    'BAJFINANCE': 6750,
    'HCLTECH': 1245,
    'TATAMOTORS': 485,
    'HINDUNILVR': 2450,
    'KOTAKBANK': 1820
  };

  const basePrice = basePrices[symbol.toUpperCase()] || 1000;
  const change = (Math.random() - 0.5) * 100; // Random change ±50
  const changePercent = (change / basePrice) * 100;
  const currentPrice = basePrice + change;

  res.json({
    success: true,
    data: {
      symbol: symbol.toUpperCase(),
      price: Math.round(currentPrice * 100) / 100,
      change: Math.round(change * 100) / 100,
      changePercent: Math.round(changePercent * 100) / 100,
      timestamp: new Date(),
      volume: Math.floor(Math.random() * 1000000) + 100000,
      source: 'Indian Market API (Mock)'
    },
    message: `Current price for ${symbol.toUpperCase()}`
  });
});

app.get('/api/market-data/historical/:symbol', async (req, res) => {
  const { symbol } = req.params;
  const { period = '1yr', filter = 'price' } = req.query;

  const useRealAPI = process.env.USE_REAL_INDIAN_API === 'true';

  if (useRealAPI) {
    // Try to fetch from real Indian Market API
    try {
      const axios = require('axios');
      const apiUrl = process.env.INDIAN_MARKET_API_URL || 'https://stock.indianapi.in';

      console.log(`📈 Fetching real data for ${symbol} from ${apiUrl}`);

      const headers = {
        'Accept': 'application/json',
        'User-Agent': 'Kuber.ai/1.0'
      };

      if (process.env.INDIAN_MARKET_API_KEY) {
        headers['X-Api-Key'] = process.env.INDIAN_MARKET_API_KEY;
      }

      const response = await axios.get(`${apiUrl}/historical_data`, {
        params: {
          stock_name: symbol,
          period,
          filter
        },
        timeout: 30000,
        headers
      });

      // Parse the real API response
      const datasets = response.data.datasets || [];
      const priceDataset = datasets.find(d => d.metric === 'Price');
      const volumeDataset = datasets.find(d => d.metric === 'Volume');

      if (!priceDataset) {
        throw new Error('No price data in API response');
      }

      const marketData = [];

      for (let i = 0; i < priceDataset.values.length; i++) {
        const priceEntry = priceDataset.values[i];
        const volumeEntry = volumeDataset?.values[i];

        const date = new Date(priceEntry[0]);
        const price = parseFloat(priceEntry[1]);
        const volume = volumeEntry ? (volumeEntry[1] || 0) : 0;

        if (!isNaN(date.getTime()) && !isNaN(price) && price > 0) {
          const dailyVariation = price * 0.005;
          const open = price + (Math.random() - 0.5) * dailyVariation;
          const close = price;
          const high = Math.max(open, close) + Math.random() * dailyVariation;
          const low = Math.min(open, close) - Math.random() * dailyVariation;

          marketData.push({
            symbol,
            timestamp: date,
            open: Math.round(open * 100) / 100,
            high: Math.round(high * 100) / 100,
            low: Math.round(low * 100) / 100,
            close: price,
            volume: Math.max(volume, 0),
            adjustedClose: price
          });
        }
      }

      return res.json({
        success: true,
        data: marketData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)),
        message: `Real historical data for ${symbol} (${marketData.length} data points)`,
        source: 'Indian Market API'
      });

    } catch (apiError) {
      console.error(`❌ Real API failed for ${symbol}:`, apiError.message);
      // Fall back to mock data
      console.log(`🔄 Falling back to mock data for ${symbol}`);
    }
  }

  // Generate mock historical data (fallback or when USE_REAL_INDIAN_API=false)
  const generateHistoricalData = (symbol, period) => {
    const basePrices = {
      'RELIANCE': 2500,
      'TCS': 3200,
      'HDFCBANK': 1600,
      'INFY': 1400,
      'ICICIBANK': 900,
      'SBIN': 550,
      'BHARTIARTL': 800,
      'ITC': 450,
      'WIPRO': 400,
      'MARUTI': 9500,
      'BAJFINANCE': 6500,
      'HCLTECH': 1200,
      'TATAMOTORS': 500,
      'HINDUNILVR': 2400,
      'KOTAKBANK': 1800
    };

    const days = period === '1m' ? 30 : period === '6m' ? 180 : period === '1yr' ? 365 : 365;
    const data = [];
    let basePrice = basePrices[symbol] || 1000;

    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;

      const volatility = 0.02;
      const randomChange = (Math.random() - 0.5) * volatility;
      basePrice = basePrice * (1 + randomChange);

      const open = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const close = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const high = Math.max(open, close) * (1 + Math.random() * 0.02);
      const low = Math.min(open, close) * (1 - Math.random() * 0.02);

      data.push({
        symbol,
        timestamp: date,
        open: Math.round(open * 100) / 100,
        high: Math.round(high * 100) / 100,
        low: Math.round(low * 100) / 100,
        close: Math.round(close * 100) / 100,
        volume: Math.floor(Math.random() * 1000000) + 100000,
        adjustedClose: Math.round(close * 100) / 100
      });
    }

    return data;
  };

  const data = generateHistoricalData(symbol.toUpperCase(), period);

  res.json({
    success: true,
    data,
    message: `Historical data for ${symbol.toUpperCase()} (${data.length} data points)`
  });
});

// Error handling middleware
app.use((req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
});

app.use((err, req, res, next) => {
  res.status(res.statusCode === 200 ? 500 : res.statusCode).json({
    success: false,
    error: err.message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
  console.log(`🤖 NLP Strategy API ready at http://localhost:${PORT}/api/nlp-strategy`);
});
