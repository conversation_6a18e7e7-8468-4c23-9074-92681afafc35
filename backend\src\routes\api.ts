import { Router } from 'express';
import strategyRoutes from './strategies';
import marketDataRoutes from './marketData';
import portfolioRoutes from './portfolio';
import backtestRoutes from './backtest';
import nlpStrategyRoutes from './nlpStrategy';

const router = Router();

// API version and info
router.get('/', (req, res) => {
  res.json({
    message: 'Kuber.ai API v1.0',
    version: '1.0.0',
    endpoints: {
      strategies: '/api/strategies',
      marketData: '/api/market-data',
      portfolio: '/api/portfolio',
      backtest: '/api/backtest',
      nlpStrategy: '/api/nlp-strategy'
    }
  });
});

// Route modules
router.use('/strategies', strategyRoutes);
router.use('/market-data', marketDataRoutes);
router.use('/portfolio', portfolioRoutes);
router.use('/backtest', backtestRoutes);
router.use('/nlp-strategy', nlpStrategyRoutes);

export default router;
