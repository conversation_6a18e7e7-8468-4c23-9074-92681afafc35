import { Router, Request, Response } from 'express';
import backtestingEngine from '../services/backtesting';
import db from '../services/database';
import { ApiResponse, BacktestRequest } from '../types';

const router = Router();

/**
 * POST /api/backtest/run
 * Run a backtest for a strategy
 */
router.post('/run', async (req: Request, res: Response) => {
  try {
    const { strategyId, symbol, startDate, endDate, initialCapital, parameters } = req.body;

    // Validate required fields
    if (!strategyId || !symbol || !startDate || !endDate || !initialCapital) {
      const response: ApiResponse = {
        success: false,
        error: 'strategyId, symbol, startDate, endDate, and initialCapital are required'
      };
      return res.status(400).json(response);
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid date format'
      };
      return res.status(400).json(response);
    }

    if (start >= end) {
      const response: ApiResponse = {
        success: false,
        error: 'Start date must be before end date'
      };
      return res.status(400).json(response);
    }

    // Validate initial capital
    if (initialCapital <= 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Initial capital must be greater than 0'
      };
      return res.status(400).json(response);
    }

    // Check if strategy exists
    const strategy = await db.prisma.strategy.findUnique({
      where: { id: strategyId }
    });

    if (!strategy) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy not found'
      };
      return res.status(404).json(response);
    }

    // Create backtest request
    const backtestRequest: BacktestRequest = {
      strategyId,
      symbol: symbol.toUpperCase(),
      startDate: start,
      endDate: end,
      initialCapital,
      parameters
    };

    // Run backtest
    const result = await backtestingEngine.runBacktest(backtestRequest);

    const response: ApiResponse = {
      success: true,
      data: result,
      message: `Backtest completed for strategy "${strategy.name}" on ${symbol.toUpperCase()}`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/backtest/results/:strategyId
 * Get backtest results for a strategy
 */
router.get('/results/:strategyId', async (req: Request, res: Response) => {
  try {
    const { strategyId } = req.params;
    const { limit = '10', offset = '0' } = req.query;

    const results = await db.prisma.backtestResult.findMany({
      where: { strategyId },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string)
    });

    const total = await db.prisma.backtestResult.count({
      where: { strategyId }
    });

    const response: ApiResponse = {
      success: true,
      data: results,
      message: `Found ${results.length} backtest results`
    };

    // Add pagination info
    (response as any).pagination = {
      total,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      hasMore: total > parseInt(offset as string) + parseInt(limit as string)
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/backtest/result/:id
 * Get a specific backtest result
 */
router.get('/result/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const result = await db.prisma.backtestResult.findUnique({
      where: { id },
      include: {
        strategy: {
          select: {
            id: true,
            name: true,
            description: true,
            asset: true
          }
        }
      }
    });

    if (!result) {
      const response: ApiResponse = {
        success: false,
        error: 'Backtest result not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: result,
      message: 'Backtest result retrieved successfully'
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/backtest/results
 * Get all backtest results for the user
 */
router.get('/results', async (req: Request, res: Response) => {
  try {
    const { limit = '20', offset = '0', symbol, strategyId } = req.query;

    // Get default user
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const where: any = { userId: defaultUser.id };
    if (symbol) where.symbol = symbol;
    if (strategyId) where.strategyId = strategyId;

    const results = await db.prisma.backtestResult.findMany({
      where,
      include: {
        strategy: {
          select: {
            id: true,
            name: true,
            description: true,
            asset: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string)
    });

    const total = await db.prisma.backtestResult.count({ where });

    const response: ApiResponse = {
      success: true,
      data: results,
      message: `Found ${results.length} backtest results`
    };

    // Add pagination info
    (response as any).pagination = {
      total,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      hasMore: total > parseInt(offset as string) + parseInt(limit as string)
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * DELETE /api/backtest/result/:id
 * Delete a backtest result
 */
router.delete('/result/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const result = await db.prisma.backtestResult.findUnique({
      where: { id }
    });

    if (!result) {
      const response: ApiResponse = {
        success: false,
        error: 'Backtest result not found'
      };
      return res.status(404).json(response);
    }

    await db.prisma.backtestResult.delete({
      where: { id }
    });

    const response: ApiResponse = {
      success: true,
      message: 'Backtest result deleted successfully'
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/backtest/quick
 * Run a quick backtest with default parameters
 */
router.post('/quick', async (req: Request, res: Response) => {
  try {
    const { symbol, days = 30 } = req.body;

    if (!symbol) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbol is required'
      };
      return res.status(400).json(response);
    }

    // Create a simple MACD strategy for quick testing
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(days));

    // Get or create a default strategy
    let strategy = await db.prisma.strategy.findFirst({
      where: { name: 'Quick Test MACD' }
    });

    if (!strategy) {
      const defaultUser = await db.prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });

      if (!defaultUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found'
        };
        return res.status(404).json(response);
      }

      strategy = await db.prisma.strategy.create({
        data: {
          name: 'Quick Test MACD',
          description: 'Quick MACD strategy for testing',
          asset: symbol.toUpperCase(),
          status: 'STOPPED',
          pnl: 0,
          parameters: {
            entryConditions: [
              { type: 'MACD_CROSS', parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 } }
            ],
            exitConditions: [
              { type: 'STOP_LOSS', parameters: { percentage: 5 } },
              { type: 'TAKE_PROFIT', parameters: { percentage: 10 } }
            ],
            riskManagement: {
              stopLossPercentage: 5,
              takeProfitPercentage: 10,
              maxDrawdownPercentage: 15,
              positionSizing: 'PERCENTAGE'
            },
            timeframe: '1d',
            maxPositionSize: 5000
          },
          userId: defaultUser.id
        }
      });
    }

    const backtestRequest: BacktestRequest = {
      strategyId: strategy.id,
      symbol: symbol.toUpperCase(),
      startDate,
      endDate,
      initialCapital: 10000
    };

    const result = await backtestingEngine.runBacktest(backtestRequest);

    const response: ApiResponse = {
      success: true,
      data: result,
      message: `Quick backtest completed for ${symbol.toUpperCase()} over ${days} days`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

export default router;
