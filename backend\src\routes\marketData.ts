import { Router, Request, Response } from 'express';
import marketDataService from '../services/marketData';
import { ApiResponse } from '../types';

const router = Router();

/**
 * GET /api/market-data/current/:symbol
 * Get current price for a symbol
 */
router.get('/current/:symbol', async (req: Request, res: Response): Promise<any> => {
  try {
    const { symbol } = req.params;
    const price = await marketDataService.getCurrentPrice(symbol.toUpperCase());
    
    const response: ApiResponse = {
      success: true,
      data: price,
      message: `Current price for ${symbol.toUpperCase()}`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/current
 * Get current prices for multiple symbols
 */
router.post('/current', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    const prices = await marketDataService.getCurrentPrices(
      symbols.map(s => s.toUpperCase())
    );
    
    const response: ApiResponse = {
      success: true,
      data: prices,
      message: `Current prices for ${symbols.length} symbols`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/historical/:symbol
 * Get historical data for a symbol
 */
router.get('/historical/:symbol', async (req: Request, res: Response) => {
  try {
    const { symbol } = req.params;
    const { startDate, endDate, interval = '1d', source = 'api' } = req.query;
    
    if (!startDate || !endDate) {
      const response: ApiResponse = {
        success: false,
        error: 'startDate and endDate are required'
      };
      return res.status(400).json(response);
    }
    
    const start = new Date(startDate as string);
    const end = new Date(endDate as string);
    
    let data;
    if (source === 'database') {
      // Get from database
      data = await marketDataService.getStoredHistoricalData(
        symbol.toUpperCase(),
        start,
        end
      );
    } else {
      // Get from API (and store in database)
      data = await marketDataService.getHistoricalData(
        symbol.toUpperCase(),
        start,
        end,
        interval as '1d' | '1wk' | '1mo'
      );
    }
    
    const response: ApiResponse = {
      success: true,
      data,
      message: `Historical data for ${symbol.toUpperCase()} from ${start.toDateString()} to ${end.toDateString()}`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/search
 * Search for symbols
 */
router.get('/search', async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    
    if (!q || typeof q !== 'string') {
      const response: ApiResponse = {
        success: false,
        error: 'Query parameter "q" is required'
      };
      return res.status(400).json(response);
    }
    
    const results = await marketDataService.searchSymbols(q);
    
    const response: ApiResponse = {
      success: true,
      data: results,
      message: `Search results for "${q}"`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/subscribe
 * Subscribe to real-time price updates for symbols
 */
router.post('/subscribe', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    symbols.forEach(symbol => {
      marketDataService.subscribeToSymbol(symbol.toUpperCase());
    });
    
    const response: ApiResponse = {
      success: true,
      data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
      message: `Subscribed to ${symbols.length} symbols for real-time updates`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/unsubscribe
 * Unsubscribe from real-time price updates for symbols
 */
router.post('/unsubscribe', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    symbols.forEach(symbol => {
      marketDataService.unsubscribeFromSymbol(symbol.toUpperCase());
    });
    
    const response: ApiResponse = {
      success: true,
      data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
      message: `Unsubscribed from ${symbols.length} symbols`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/subscriptions
 * Get list of currently subscribed symbols
 */
router.get('/subscriptions', (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
    message: 'Currently subscribed symbols'
  };
  
  res.json(response);
});

export default router;
