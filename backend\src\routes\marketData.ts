import { Router, Request, Response } from 'express';
import marketDataService from '../services/marketData';
import indianMarketDataService from '../services/indianMarketData';
import { ApiResponse } from '../types';

const router = Router();

/**
 * GET /api/market-data/current/:symbol
 * Get current price for a symbol (prioritizes Indian market data)
 */
router.get('/current/:symbol', async (req: Request, res: Response): Promise<any> => {
  try {
    const { symbol } = req.params;

    // Try Indian market data first for Indian stocks
    const indianStocks = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL', 'ITC', 'WIPRO', 'MARUTI', 'BAJFINANCE', 'HCLTECH', 'TATAMOTORS', 'HINDUNILVR', 'KOTAKBANK'];

    let price;
    if (indianStocks.includes(symbol.toUpperCase())) {
      try {
        const indianPrice = await indianMarketDataService.getCurrentPrice(symbol.toUpperCase());
        price = {
          symbol: symbol.toUpperCase(),
          price: indianPrice.price,
          change: indianPrice.change,
          changePercent: indianPrice.changePercent,
          timestamp: new Date(),
          volume: 0,
          source: 'Indian Market API'
        };
      } catch (error) {
        console.log(`Falling back to global market data for ${symbol}`);
        price = await marketDataService.getCurrentPrice(symbol.toUpperCase());
        price.source = 'Global Market API';
      }
    } else {
      price = await marketDataService.getCurrentPrice(symbol.toUpperCase());
      price.source = 'Global Market API';
    }

    const response: ApiResponse = {
      success: true,
      data: price,
      message: `Current price for ${symbol.toUpperCase()}`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/current
 * Get current prices for multiple symbols
 */
router.post('/current', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    const prices = await marketDataService.getCurrentPrices(
      symbols.map(s => s.toUpperCase())
    );
    
    const response: ApiResponse = {
      success: true,
      data: prices,
      message: `Current prices for ${symbols.length} symbols`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/historical/:symbol
 * Get historical data for a symbol (prioritizes Indian market data)
 */
router.get('/historical/:symbol', async (req: Request, res: Response) => {
  try {
    const { symbol } = req.params;
    const { startDate, endDate, period = '1yr', filter = 'price', source = 'api' } = req.query;

    // Indian market data uses period instead of startDate/endDate
    const indianStocks = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL', 'ITC', 'WIPRO', 'MARUTI', 'BAJFINANCE', 'HCLTECH', 'TATAMOTORS', 'HINDUNILVR', 'KOTAKBANK'];

    let data;
    if (indianStocks.includes(symbol.toUpperCase())) {
      try {
        // Use Indian market data API
        data = await indianMarketDataService.getHistoricalData(
          symbol.toUpperCase(),
          period as '1m' | '6m' | '1yr' | '3yr' | '5yr' | '10yr' | 'max',
          filter as 'default' | 'price' | 'pe' | 'sm' | 'evebitda' | 'ptb' | 'mcs'
        );

        console.log(`✅ Retrieved ${data.length} data points from Indian market API for ${symbol}`);
      } catch (error) {
        console.log(`Falling back to global market data for ${symbol}`);

        // Fallback to global market data
        if (startDate && endDate) {
          const start = new Date(startDate as string);
          const end = new Date(endDate as string);

          if (source === 'database') {
            data = await marketDataService.getStoredHistoricalData(symbol.toUpperCase(), start, end);
          } else {
            data = await marketDataService.getHistoricalData(symbol.toUpperCase(), start, end);
          }
        } else {
          throw new Error('startDate and endDate are required for global market data');
        }
      }
    } else {
      // Use global market data for non-Indian stocks
      if (!startDate || !endDate) {
        const response: ApiResponse = {
          success: false,
          error: 'startDate and endDate are required for global market data'
        };
        return res.status(400).json(response);
      }

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      if (source === 'database') {
        data = await marketDataService.getStoredHistoricalData(symbol.toUpperCase(), start, end);
      } else {
        data = await marketDataService.getHistoricalData(symbol.toUpperCase(), start, end);
      }
    }

    const response: ApiResponse = {
      success: true,
      data,
      message: `Historical data for ${symbol.toUpperCase()} (${data.length} data points)`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/search
 * Search for symbols (prioritizes Indian stocks)
 */
router.get('/search', async (req: Request, res: Response) => {
  try {
    const { q } = req.query;

    if (!q || typeof q !== 'string') {
      const response: ApiResponse = {
        success: false,
        error: 'Query parameter "q" is required'
      };
      return res.status(400).json(response);
    }

    // Search Indian stocks first
    const indianResults = await indianMarketDataService.searchSymbols(q);

    // Also search global stocks
    let globalResults = [];
    try {
      globalResults = await marketDataService.searchSymbols(q);
    } catch (error) {
      console.log('Global search failed, using Indian results only');
    }

    // Combine results, prioritizing Indian stocks
    const combinedResults = [
      ...indianResults.map(stock => ({ ...stock, priority: 1, market: 'Indian' })),
      ...globalResults.map(stock => ({ ...stock, priority: 2, market: 'Global' }))
    ];

    const response: ApiResponse = {
      success: true,
      data: combinedResults,
      message: `Search results for "${q}" (${combinedResults.length} results)`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/subscribe
 * Subscribe to real-time price updates for symbols
 */
router.post('/subscribe', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    symbols.forEach(symbol => {
      marketDataService.subscribeToSymbol(symbol.toUpperCase());
    });
    
    const response: ApiResponse = {
      success: true,
      data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
      message: `Subscribed to ${symbols.length} symbols for real-time updates`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/market-data/unsubscribe
 * Unsubscribe from real-time price updates for symbols
 */
router.post('/unsubscribe', async (req: Request, res: Response) => {
  try {
    const { symbols } = req.body;
    
    if (!Array.isArray(symbols) || symbols.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbols array is required'
      };
      return res.status(400).json(response);
    }
    
    symbols.forEach(symbol => {
      marketDataService.unsubscribeFromSymbol(symbol.toUpperCase());
    });
    
    const response: ApiResponse = {
      success: true,
      data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
      message: `Unsubscribed from ${symbols.length} symbols`
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/market-data/subscriptions
 * Get list of currently subscribed symbols
 */
router.get('/subscriptions', (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    data: { subscribedSymbols: marketDataService.getSubscribedSymbols() },
    message: 'Currently subscribed symbols'
  };
  
  res.json(response);
});

export default router;
