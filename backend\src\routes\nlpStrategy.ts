import { Router, Request, Response } from 'express';
import strategyParserService from '../services/strategyParser';
import backtestingEngine from '../services/backtesting';
import indianMarketDataService from '../services/indianMarketData';
import db from '../services/database';
import { ApiResponse, BacktestRequest } from '../types';

const router = Router();

/**
 * POST /api/nlp-strategy/parse
 * Parse natural language into strategy parameters
 */
router.post('/parse', async (req: Request, res: Response) => {
  try {
    const { naturalLanguage } = req.body;

    if (!naturalLanguage || typeof naturalLanguage !== 'string') {
      const response: ApiResponse = {
        success: false,
        error: 'Natural language description is required'
      };
      return res.status(400).json(response);
    }

    // Parse the natural language
    const parsedStrategy = strategyParserService.parseStrategy(naturalLanguage);
    
    // Validate the parsed strategy
    const validation = strategyParserService.validateStrategy(parsedStrategy);

    const response: ApiResponse = {
      success: true,
      data: {
        strategy: parsedStrategy,
        validation
      },
      message: 'Strategy parsed successfully'
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/nlp-strategy/create-and-backtest
 * Create strategy from natural language and run backtest
 */
router.post('/create-and-backtest', async (req: Request, res: Response) => {
  try {
    const { naturalLanguage, backtestPeriod = '1yr', initialCapital = 100000 } = req.body;

    if (!naturalLanguage || typeof naturalLanguage !== 'string') {
      const response: ApiResponse = {
        success: false,
        error: 'Natural language description is required'
      };
      return res.status(400).json(response);
    }

    // Parse the natural language
    const parsedStrategy = strategyParserService.parseStrategy(naturalLanguage);
    
    // Validate the parsed strategy
    const validation = strategyParserService.validateStrategy(parsedStrategy);
    
    if (!validation.isValid) {
      const response: ApiResponse = {
        success: false,
        error: `Strategy validation failed: ${validation.errors.join(', ')}`
      };
      return res.status(400).json(response);
    }

    // Get default user
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    // Create the strategy in database
    const strategy = await db.prisma.strategy.create({
      data: {
        name: parsedStrategy.name,
        description: parsedStrategy.description,
        asset: parsedStrategy.asset,
        status: 'STOPPED',
        pnl: 0,
        parameters: parsedStrategy.parameters as any,
        userId: defaultUser.id
      }
    });

    // Fetch historical data for backtesting
    console.log(`📊 Fetching historical data for ${parsedStrategy.asset}`);
    
    try {
      await indianMarketDataService.getHistoricalData(
        parsedStrategy.asset,
        backtestPeriod as any
      );
    } catch (dataError) {
      console.warn('Could not fetch fresh data, proceeding with existing data');
    }

    // Set up backtest dates
    const endDate = new Date();
    const startDate = new Date();
    
    switch (backtestPeriod) {
      case '1m':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '6m':
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case '1yr':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case '3yr':
        startDate.setFullYear(endDate.getFullYear() - 3);
        break;
      default:
        startDate.setFullYear(endDate.getFullYear() - 1);
    }

    // Run backtest
    console.log(`🔄 Running backtest for ${strategy.name}`);
    
    const backtestRequest: BacktestRequest = {
      strategyId: strategy.id,
      symbol: parsedStrategy.asset,
      startDate,
      endDate,
      initialCapital,
      parameters: parsedStrategy.parameters
    };

    const backtestResult = await backtestingEngine.runBacktest(backtestRequest);

    const response: ApiResponse = {
      success: true,
      data: {
        strategy,
        backtestResult,
        validation
      },
      message: `Strategy "${parsedStrategy.name}" created and backtested successfully`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/nlp-strategy/deploy
 * Deploy a strategy to live environment
 */
router.post('/deploy', async (req: Request, res: Response) => {
  try {
    const { strategyId } = req.body;

    if (!strategyId) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy ID is required'
      };
      return res.status(400).json(response);
    }

    // Get strategy
    const strategy = await db.prisma.strategy.findUnique({
      where: { id: strategyId }
    });

    if (!strategy) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy not found'
      };
      return res.status(404).json(response);
    }

    // Update strategy status to active
    const updatedStrategy = await db.prisma.strategy.update({
      where: { id: strategyId },
      data: { status: 'ACTIVE' }
    });

    // In a real implementation, you would:
    // 1. Set up real-time data feeds
    // 2. Initialize position tracking
    // 3. Set up order execution system
    // 4. Start monitoring for entry/exit signals

    console.log(`🚀 Strategy "${strategy.name}" deployed to live environment`);

    const response: ApiResponse = {
      success: true,
      data: updatedStrategy,
      message: `Strategy "${strategy.name}" deployed successfully to live environment`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * GET /api/nlp-strategy/examples
 * Get example natural language strategy descriptions
 */
router.get('/examples', (req: Request, res: Response) => {
  const examples = [
    {
      category: 'MACD Strategies',
      examples: [
        'Buy RELIANCE when MACD crosses above signal line, stop loss at 5%, take profit at 15%',
        'Buy TCS when MACD histogram turns positive, hold for 30 days maximum',
        'Buy INFY when MACD crosses above signal with volume 2x average, stop loss 3%'
      ]
    },
    {
      category: 'RSI Strategies',
      examples: [
        'Buy HDFC when RSI drops below 30, sell when RSI goes above 70',
        'Buy TATAMOTORS when RSI below 25, stop loss 4%, take profit 12%',
        'Buy ICICIBANK when RSI oversold below 30, hold for 15 days'
      ]
    },
    {
      category: 'Breakout Strategies',
      examples: [
        'Buy BHARTIARTL on volume breakout above resistance with 3x volume, stop loss 6%',
        'Buy WIPRO when price breaks above 20-day moving average, take profit 10%',
        'Buy MARUTI on price breakout with volume spike, stop loss 5%, target 20%'
      ]
    },
    {
      category: 'Combined Strategies',
      examples: [
        'Buy BAJFINANCE when MACD crosses above and RSI below 40, stop loss 5%, take profit 18%',
        'Buy SBIN when price above moving average and volume 2x normal, hold 20 days max',
        'Buy ITC when RSI oversold and MACD positive, stop loss 4%, take profit 15%'
      ]
    }
  ];

  const response: ApiResponse = {
    success: true,
    data: examples,
    message: 'Example strategy descriptions'
  };

  return res.json(response);
});

/**
 * POST /api/nlp-strategy/quick-test
 * Quick test of natural language parsing without creating strategy
 */
router.post('/quick-test', async (req: Request, res: Response) => {
  try {
    const { naturalLanguage } = req.body;

    if (!naturalLanguage || typeof naturalLanguage !== 'string') {
      const response: ApiResponse = {
        success: false,
        error: 'Natural language description is required'
      };
      return res.status(400).json(response);
    }

    // Parse the natural language
    const parsedStrategy = strategyParserService.parseStrategy(naturalLanguage);
    
    // Validate the parsed strategy
    const validation = strategyParserService.validateStrategy(parsedStrategy);

    // Generate code representation
    const codeRepresentation = this.generateCodeRepresentation(parsedStrategy);

    const response: ApiResponse = {
      success: true,
      data: {
        originalText: naturalLanguage,
        parsedStrategy,
        validation,
        codeRepresentation
      },
      message: 'Strategy parsing test completed'
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * Generate code representation of strategy
 */
function generateCodeRepresentation(strategy: any): string {
  return `
// Generated Trading Strategy: ${strategy.name}
const strategy = {
  name: "${strategy.name}",
  asset: "${strategy.asset}",
  timeframe: "${strategy.parameters.timeframe}",
  
  entryConditions: ${JSON.stringify(strategy.parameters.entryConditions, null, 2)},
  
  exitConditions: ${JSON.stringify(strategy.parameters.exitConditions, null, 2)},
  
  riskManagement: ${JSON.stringify(strategy.parameters.riskManagement, null, 2)},
  
  maxPositionSize: ${strategy.parameters.maxPositionSize}
};

// Entry Logic
function checkEntry(marketData, indicators) {
  // Implementation based on entry conditions
  return entrySignal;
}

// Exit Logic  
function checkExit(position, marketData, indicators) {
  // Implementation based on exit conditions
  return exitSignal;
}
`.trim();
}

export default router;
