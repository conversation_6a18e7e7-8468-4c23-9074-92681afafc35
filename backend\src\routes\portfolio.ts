import { Router, Request, Response } from 'express';
import db from '../services/database';
import marketDataService from '../services/marketData';
import { ApiResponse } from '../types';

const router = Router();

/**
 * GET /api/portfolio
 * Get portfolio overview
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    // Get default user's portfolio
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const portfolio = await db.prisma.portfolio.findFirst({
      where: { userId: defaultUser.id },
      include: {
        holdings: true
      }
    });

    if (!portfolio) {
      const response: ApiResponse = {
        success: false,
        error: 'Portfolio not found'
      };
      return res.status(404).json(response);
    }

    // Update current prices for holdings
    const symbols = portfolio.holdings.map(h => h.symbol);
    if (symbols.length > 0) {
      try {
        const currentPrices = await marketDataService.getCurrentPrices(symbols);
        
        // Update holdings with current prices
        for (const holding of portfolio.holdings) {
          const currentPrice = currentPrices.find(p => p.symbol === holding.symbol);
          if (currentPrice) {
            const marketValue = holding.quantity * currentPrice.price;
            const unrealizedPnL = marketValue - (holding.quantity * holding.averagePrice);
            const unrealizedPnLPercent = (unrealizedPnL / (holding.quantity * holding.averagePrice)) * 100;

            await db.prisma.holding.update({
              where: { id: holding.id },
              data: {
                currentPrice: currentPrice.price,
                marketValue,
                unrealizedPnL,
                unrealizedPnLPercent
              }
            });

            // Update the holding object for response
            holding.currentPrice = currentPrice.price;
            holding.marketValue = marketValue;
            holding.unrealizedPnL = unrealizedPnL;
            holding.unrealizedPnLPercent = unrealizedPnLPercent;
          }
        }

        // Update portfolio totals
        const totalMarketValue = portfolio.holdings.reduce((sum, h) => sum + h.marketValue, 0);
        const totalUnrealizedPnL = portfolio.holdings.reduce((sum, h) => sum + h.unrealizedPnL, 0);
        const newTotalValue = portfolio.cash + totalMarketValue;

        await db.prisma.portfolio.update({
          where: { id: portfolio.id },
          data: {
            totalValue: newTotalValue,
            totalPnL: totalUnrealizedPnL
          }
        });

        portfolio.totalValue = newTotalValue;
        portfolio.totalPnL = totalUnrealizedPnL;
      } catch (error) {
        console.error('Error updating portfolio prices:', error);
        // Continue with stale prices if update fails
      }
    }

    const response: ApiResponse = {
      success: true,
      data: portfolio,
      message: 'Portfolio retrieved successfully'
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/portfolio/holdings
 * Get portfolio holdings
 */
router.get('/holdings', async (req: Request, res: Response) => {
  try {
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const portfolio = await db.prisma.portfolio.findFirst({
      where: { userId: defaultUser.id }
    });

    if (!portfolio) {
      const response: ApiResponse = {
        success: false,
        error: 'Portfolio not found'
      };
      return res.status(404).json(response);
    }

    const holdings = await db.prisma.holding.findMany({
      where: { portfolioId: portfolio.id },
      orderBy: { marketValue: 'desc' }
    });

    const response: ApiResponse = {
      success: true,
      data: holdings,
      message: `Found ${holdings.length} holdings`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * POST /api/portfolio/holdings
 * Add or update a holding
 */
router.post('/holdings', async (req: Request, res: Response) => {
  try {
    const { symbol, quantity, averagePrice } = req.body;

    if (!symbol || quantity === undefined || averagePrice === undefined) {
      const response: ApiResponse = {
        success: false,
        error: 'Symbol, quantity, and averagePrice are required'
      };
      return res.status(400).json(response);
    }

    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const portfolio = await db.prisma.portfolio.findFirst({
      where: { userId: defaultUser.id }
    });

    if (!portfolio) {
      const response: ApiResponse = {
        success: false,
        error: 'Portfolio not found'
      };
      return res.status(404).json(response);
    }

    // Get current price
    let currentPrice = averagePrice;
    try {
      const priceData = await marketDataService.getCurrentPrice(symbol.toUpperCase());
      currentPrice = priceData.price;
    } catch (error) {
      console.warn(`Could not fetch current price for ${symbol}, using average price`);
    }

    const marketValue = quantity * currentPrice;
    const unrealizedPnL = marketValue - (quantity * averagePrice);
    const unrealizedPnLPercent = quantity > 0 ? (unrealizedPnL / (quantity * averagePrice)) * 100 : 0;

    const holding = await db.prisma.holding.upsert({
      where: {
        portfolioId_symbol: {
          portfolioId: portfolio.id,
          symbol: symbol.toUpperCase()
        }
      },
      update: {
        quantity,
        averagePrice,
        currentPrice,
        marketValue,
        unrealizedPnL,
        unrealizedPnLPercent
      },
      create: {
        portfolioId: portfolio.id,
        symbol: symbol.toUpperCase(),
        quantity,
        averagePrice,
        currentPrice,
        marketValue,
        unrealizedPnL,
        unrealizedPnLPercent
      }
    });

    const response: ApiResponse = {
      success: true,
      data: holding,
      message: `Holding for ${symbol.toUpperCase()} ${quantity > 0 ? 'added' : 'updated'}`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

/**
 * DELETE /api/portfolio/holdings/:symbol
 * Remove a holding
 */
router.delete('/holdings/:symbol', async (req: Request, res: Response) => {
  try {
    const { symbol } = req.params;

    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const portfolio = await db.prisma.portfolio.findFirst({
      where: { userId: defaultUser.id }
    });

    if (!portfolio) {
      const response: ApiResponse = {
        success: false,
        error: 'Portfolio not found'
      };
      return res.status(404).json(response);
    }

    const holding = await db.prisma.holding.findUnique({
      where: {
        portfolioId_symbol: {
          portfolioId: portfolio.id,
          symbol: symbol.toUpperCase()
        }
      }
    });

    if (!holding) {
      const response: ApiResponse = {
        success: false,
        error: 'Holding not found'
      };
      return res.status(404).json(response);
    }

    await db.prisma.holding.delete({
      where: { id: holding.id }
    });

    const response: ApiResponse = {
      success: true,
      message: `Holding for ${symbol.toUpperCase()} removed`
    };

    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

export default router;
