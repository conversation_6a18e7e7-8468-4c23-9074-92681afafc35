import { Router, Request, Response } from 'express';
import db from '../services/database';
import { ApiResponse, Strategy } from '../types';

const router = Router();

/**
 * GET /api/strategies
 * Get all strategies for a user
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    // For now, get strategies for the default user
    // In a real app, you'd get this from authentication middleware
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const strategies = await db.prisma.strategy.findMany({
      where: { userId: defaultUser.id },
      orderBy: { createdAt: 'desc' }
    });

    const response: ApiResponse = {
      success: true,
      data: strategies,
      message: `Found ${strategies.length} strategies`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * GET /api/strategies/:id
 * Get a specific strategy
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const strategy = await db.prisma.strategy.findUnique({
      where: { id },
      include: {
        trades: {
          orderBy: { timestamp: 'desc' },
          take: 10 // Get last 10 trades
        },
        backtests: {
          orderBy: { createdAt: 'desc' },
          take: 5 // Get last 5 backtest results
        }
      }
    });

    if (!strategy) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy ${strategy.name} retrieved`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/strategies
 * Create a new strategy
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, description, asset, parameters } = req.body;

    // Validate required fields
    if (!name || !description || !asset || !parameters) {
      const response: ApiResponse = {
        success: false,
        error: 'Name, description, asset, and parameters are required'
      };
      return res.status(400).json(response);
    }

    // Get default user
    const defaultUser = await db.prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      return res.status(404).json(response);
    }

    const strategy = await db.prisma.strategy.create({
      data: {
        name,
        description,
        asset: asset.toUpperCase(),
        status: 'STOPPED',
        pnl: 0,
        parameters,
        userId: defaultUser.id
      }
    });

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy "${name}" created successfully`
    };

    return res.status(201).json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * PUT /api/strategies/:id
 * Update a strategy
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, description, asset, status, parameters } = req.body;

    const existingStrategy = await db.prisma.strategy.findUnique({
      where: { id }
    });

    if (!existingStrategy) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy not found'
      };
      return res.status(404).json(response);
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (asset !== undefined) updateData.asset = asset.toUpperCase();
    if (status !== undefined) updateData.status = status;
    if (parameters !== undefined) updateData.parameters = parameters;

    const strategy = await db.prisma.strategy.update({
      where: { id },
      data: updateData
    });

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy "${strategy.name}" updated successfully`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * DELETE /api/strategies/:id
 * Delete a strategy
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const existingStrategy = await db.prisma.strategy.findUnique({
      where: { id }
    });

    if (!existingStrategy) {
      const response: ApiResponse = {
        success: false,
        error: 'Strategy not found'
      };
      return res.status(404).json(response);
    }

    await db.prisma.strategy.delete({
      where: { id }
    });

    const response: ApiResponse = {
      success: true,
      message: `Strategy "${existingStrategy.name}" deleted successfully`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/strategies/:id/start
 * Start a strategy
 */
router.post('/:id/start', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const strategy = await db.prisma.strategy.update({
      where: { id },
      data: { status: 'ACTIVE' }
    });

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy "${strategy.name}" started`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/strategies/:id/stop
 * Stop a strategy
 */
router.post('/:id/stop', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const strategy = await db.prisma.strategy.update({
      where: { id },
      data: { status: 'STOPPED' }
    });

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy "${strategy.name}" stopped`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

/**
 * POST /api/strategies/:id/pause
 * Pause a strategy
 */
router.post('/:id/pause', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const strategy = await db.prisma.strategy.update({
      where: { id },
      data: { status: 'PAUSED' }
    });

    const response: ApiResponse = {
      success: true,
      data: strategy,
      message: `Strategy "${strategy.name}" paused`
    };

    return res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return res.status(500).json(response);
  }
});

export default router;
