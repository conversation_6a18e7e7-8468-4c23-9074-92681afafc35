import { MarketData, BacktestRequest, BacktestResult, BacktestTrade, EquityPoint, StrategyParameters } from '../types';
import marketDataService from './marketData';
import db from './database';

interface Position {
  symbol: string;
  quantity: number;
  entryPrice: number;
  entryDate: Date;
}

class BacktestingEngine {
  private static instance: BacktestingEngine;

  private constructor() {}

  public static getInstance(): BacktestingEngine {
    if (!BacktestingEngine.instance) {
      BacktestingEngine.instance = new BacktestingEngine();
    }
    return BacktestingEngine.instance;
  }

  /**
   * Run a backtest for a strategy
   */
  public async runBacktest(request: BacktestRequest): Promise<BacktestResult> {
    try {
      console.log(`🔄 Starting backtest for strategy ${request.strategyId} on ${request.symbol}`);

      // Get historical data
      const historicalData = await marketDataService.getStoredHistoricalData(
        request.symbol,
        request.startDate,
        request.endDate
      );

      if (historicalData.length === 0) {
        // Try to fetch from API if not in database
        const fetchedData = await marketDataService.getHistoricalData(
          request.symbol,
          request.startDate,
          request.endDate
        );
        historicalData.push(...fetchedData);
      }

      if (historicalData.length === 0) {
        throw new Error(`No historical data available for ${request.symbol}`);
      }

      // Get strategy parameters
      const strategy = await db.prisma.strategy.findUnique({
        where: { id: request.strategyId }
      });

      if (!strategy) {
        throw new Error(`Strategy ${request.strategyId} not found`);
      }

      const parameters = request.parameters || (strategy.parameters as StrategyParameters);

      // Run the backtest simulation
      const result = await this.simulateStrategy(
        historicalData,
        parameters,
        request.initialCapital
      );

      // Calculate performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics(
        result.trades,
        result.equityCurve,
        request.initialCapital
      );

      // Create backtest result
      const backtestResult: BacktestResult = {
        id: '', // Will be set by database
        strategyId: request.strategyId,
        symbol: request.symbol,
        startDate: request.startDate,
        endDate: request.endDate,
        initialCapital: request.initialCapital,
        finalCapital: performanceMetrics.finalCapital,
        totalReturn: performanceMetrics.totalReturn,
        totalReturnPercent: performanceMetrics.totalReturnPercent,
        maxDrawdown: performanceMetrics.maxDrawdown,
        maxDrawdownPercent: performanceMetrics.maxDrawdownPercent,
        sharpeRatio: performanceMetrics.sharpeRatio,
        winRate: performanceMetrics.winRate,
        totalTrades: performanceMetrics.totalTrades,
        winningTrades: performanceMetrics.winningTrades,
        losingTrades: performanceMetrics.losingTrades,
        averageWin: performanceMetrics.averageWin,
        averageLoss: performanceMetrics.averageLoss,
        profitFactor: performanceMetrics.profitFactor,
        trades: result.trades,
        equity: result.equityCurve,
        createdAt: new Date()
      };

      // Store result in database
      const savedResult = await db.prisma.backtestResult.create({
        data: {
          strategyId: request.strategyId,
          symbol: request.symbol,
          startDate: request.startDate,
          endDate: request.endDate,
          initialCapital: request.initialCapital,
          finalCapital: backtestResult.finalCapital,
          totalReturn: backtestResult.totalReturn,
          totalReturnPercent: backtestResult.totalReturnPercent,
          maxDrawdown: backtestResult.maxDrawdown,
          maxDrawdownPercent: backtestResult.maxDrawdownPercent,
          sharpeRatio: backtestResult.sharpeRatio,
          winRate: backtestResult.winRate,
          totalTrades: backtestResult.totalTrades,
          winningTrades: backtestResult.winningTrades,
          losingTrades: backtestResult.losingTrades,
          averageWin: backtestResult.averageWin,
          averageLoss: backtestResult.averageLoss,
          profitFactor: backtestResult.profitFactor,
          trades: backtestResult.trades,
          equityCurve: backtestResult.equity,
          parameters: parameters,
          userId: strategy.userId
        }
      });

      backtestResult.id = savedResult.id;

      console.log(`✅ Backtest completed for strategy ${request.strategyId}`);
      return backtestResult;
    } catch (error) {
      console.error('❌ Backtest failed:', error);
      throw error;
    }
  }

  /**
   * Simulate strategy execution on historical data
   */
  private async simulateStrategy(
    historicalData: MarketData[],
    parameters: StrategyParameters,
    initialCapital: number
  ): Promise<{ trades: BacktestTrade[]; equityCurve: EquityPoint[] }> {
    const trades: BacktestTrade[] = [];
    const equityCurve: EquityPoint[] = [];
    let cash = initialCapital;
    let position: Position | null = null;
    let equity = initialCapital;
    let maxEquity = initialCapital;

    // Calculate technical indicators
    const indicators = this.calculateTechnicalIndicators(historicalData);

    for (let i = 0; i < historicalData.length; i++) {
      const currentData = historicalData[i];
      const currentPrice = currentData.close;

      // Update equity
      if (position) {
        const positionValue = position.quantity * currentPrice;
        equity = cash + positionValue;
      } else {
        equity = cash;
      }

      // Track max equity for drawdown calculation
      if (equity > maxEquity) {
        maxEquity = equity;
      }

      const drawdown = maxEquity - equity;

      // Add equity point
      equityCurve.push({
        timestamp: currentData.timestamp,
        equity,
        drawdown
      });

      // Check exit conditions first
      if (position) {
        const exitSignal = this.checkExitConditions(
          currentData,
          position,
          parameters.exitConditions,
          indicators,
          i
        );

        if (exitSignal) {
          // Execute sell
          const sellValue = position.quantity * currentPrice;
          cash += sellValue;
          
          const pnl = sellValue - (position.quantity * position.entryPrice);
          
          trades.push({
            timestamp: currentData.timestamp,
            type: 'SELL',
            price: currentPrice,
            quantity: position.quantity,
            pnl,
            reason: exitSignal
          });

          position = null;
        }
      }

      // Check entry conditions if no position
      if (!position) {
        const entrySignal = this.checkEntryConditions(
          currentData,
          parameters.entryConditions,
          indicators,
          i
        );

        if (entrySignal) {
          // Calculate position size
          const positionSize = this.calculatePositionSize(
            cash,
            currentPrice,
            parameters.riskManagement
          );

          if (positionSize > 0 && cash >= positionSize * currentPrice) {
            // Execute buy
            const quantity = positionSize;
            const cost = quantity * currentPrice;
            cash -= cost;

            position = {
              symbol: currentData.symbol,
              quantity,
              entryPrice: currentPrice,
              entryDate: currentData.timestamp
            };

            trades.push({
              timestamp: currentData.timestamp,
              type: 'BUY',
              price: currentPrice,
              quantity,
              reason: entrySignal
            });
          }
        }
      }
    }

    // Close any remaining position at the end
    if (position && historicalData.length > 0) {
      const lastData = historicalData[historicalData.length - 1];
      const sellValue = position.quantity * lastData.close;
      cash += sellValue;
      
      const pnl = sellValue - (position.quantity * position.entryPrice);
      
      trades.push({
        timestamp: lastData.timestamp,
        type: 'SELL',
        price: lastData.close,
        quantity: position.quantity,
        pnl,
        reason: 'End of backtest period'
      });
    }

    return { trades, equityCurve };
  }

  /**
   * Calculate technical indicators
   */
  private calculateTechnicalIndicators(data: MarketData[]): any {
    const closes = data.map(d => d.close);
    const volumes = data.map(d => d.volume);

    return {
      sma20: this.calculateSMA(closes, 20),
      sma50: this.calculateSMA(closes, 50),
      rsi: this.calculateRSI(closes, 14),
      macd: this.calculateMACD(closes),
      volume: volumes
    };
  }

  /**
   * Simple Moving Average
   */
  private calculateSMA(prices: number[], period: number): number[] {
    const sma: number[] = [];
    for (let i = 0; i < prices.length; i++) {
      if (i < period - 1) {
        sma.push(NaN);
      } else {
        const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
        sma.push(sum / period);
      }
    }
    return sma;
  }

  /**
   * RSI calculation
   */
  private calculateRSI(prices: number[], period: number = 14): number[] {
    const rsi: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];

    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    for (let i = 0; i < gains.length; i++) {
      if (i < period - 1) {
        rsi.push(NaN);
      } else {
        const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
        const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
        
        if (avgLoss === 0) {
          rsi.push(100);
        } else {
          const rs = avgGain / avgLoss;
          rsi.push(100 - (100 / (1 + rs)));
        }
      }
    }

    return [NaN, ...rsi]; // Add NaN for first price since we start from index 1
  }

  /**
   * MACD calculation
   */
  private calculateMACD(prices: number[]): { macd: number[]; signal: number[]; histogram: number[] } {
    const ema12 = this.calculateEMA(prices, 12);
    const ema26 = this.calculateEMA(prices, 26);
    
    const macd = ema12.map((val, i) => val - ema26[i]);
    const signal = this.calculateEMA(macd, 9);
    const histogram = macd.map((val, i) => val - signal[i]);

    return { macd, signal, histogram };
  }

  /**
   * Exponential Moving Average
   */
  private calculateEMA(prices: number[], period: number): number[] {
    const ema: number[] = [];
    const multiplier = 2 / (period + 1);

    for (let i = 0; i < prices.length; i++) {
      if (i === 0) {
        ema.push(prices[i]);
      } else {
        ema.push((prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier)));
      }
    }

    return ema;
  }

  /**
   * Check entry conditions
   */
  private checkEntryConditions(
    currentData: MarketData,
    entryConditions: any[],
    indicators: any,
    index: number
  ): string | null {
    for (const condition of entryConditions) {
      switch (condition.type) {
        case 'MACD_CROSS':
          if (index > 0 && indicators.macd && indicators.macd.histogram) {
            const currentHist = indicators.macd.histogram[index];
            const prevHist = indicators.macd.histogram[index - 1];
            if (currentHist > 0 && prevHist <= 0) {
              return 'MACD bullish crossover';
            }
          }
          break;

        case 'RSI_OVERSOLD':
          if (indicators.rsi && indicators.rsi[index]) {
            const rsi = indicators.rsi[index];
            const oversoldLevel = condition.parameters?.oversoldLevel || 30;
            if (rsi < oversoldLevel) {
              return `RSI oversold (${rsi.toFixed(2)})`;
            }
          }
          break;

        case 'VOLUME_BREAKOUT':
          if (index >= 20 && indicators.volume) {
            const currentVolume = indicators.volume[index];
            const avgVolume = indicators.volume.slice(index - 20, index).reduce((a, b) => a + b, 0) / 20;
            const multiplier = condition.parameters?.multiplier || 2;
            if (currentVolume > avgVolume * multiplier) {
              return `Volume breakout (${multiplier}x average)`;
            }
          }
          break;

        case 'PRICE_BREAKOUT':
          if (index >= 20 && indicators.sma20) {
            const currentPrice = currentData.close;
            const sma20 = indicators.sma20[index];
            if (currentPrice > sma20 && !isNaN(sma20)) {
              return 'Price above 20-day SMA';
            }
          }
          break;
      }
    }
    return null;
  }

  /**
   * Check exit conditions
   */
  private checkExitConditions(
    currentData: MarketData,
    position: Position,
    exitConditions: any[],
    indicators: any,
    index: number
  ): string | null {
    const currentPrice = currentData.close;
    const entryPrice = position.entryPrice;

    for (const condition of exitConditions) {
      switch (condition.type) {
        case 'STOP_LOSS':
          const stopLossPercent = condition.parameters?.percentage || 5;
          const stopLossPrice = entryPrice * (1 - stopLossPercent / 100);
          if (currentPrice <= stopLossPrice) {
            return `Stop loss triggered (${stopLossPercent}%)`;
          }
          break;

        case 'TAKE_PROFIT':
          const takeProfitPercent = condition.parameters?.percentage || 15;
          const takeProfitPrice = entryPrice * (1 + takeProfitPercent / 100);
          if (currentPrice >= takeProfitPrice) {
            return `Take profit triggered (${takeProfitPercent}%)`;
          }
          break;

        case 'RSI_OVERBOUGHT':
          if (indicators.rsi && indicators.rsi[index]) {
            const rsi = indicators.rsi[index];
            const overboughtLevel = condition.parameters?.overboughtLevel || 70;
            if (rsi > overboughtLevel) {
              return `RSI overbought (${rsi.toFixed(2)})`;
            }
          }
          break;

        case 'TIME_BASED':
          const maxHoldDays = condition.parameters?.maxHoldDays || 30;
          const daysSinceEntry = Math.floor(
            (currentData.timestamp.getTime() - position.entryDate.getTime()) / (1000 * 60 * 60 * 24)
          );
          if (daysSinceEntry >= maxHoldDays) {
            return `Maximum hold period reached (${maxHoldDays} days)`;
          }
          break;
      }
    }
    return null;
  }

  /**
   * Calculate position size based on risk management rules
   */
  private calculatePositionSize(
    availableCash: number,
    price: number,
    riskManagement: any
  ): number {
    const maxPositionSize = riskManagement.maxPositionSize || 10000;

    switch (riskManagement.positionSizing) {
      case 'FIXED':
        return Math.min(maxPositionSize / price, availableCash / price);

      case 'PERCENTAGE':
        const percentage = riskManagement.percentage || 10;
        const positionValue = availableCash * (percentage / 100);
        return Math.min(positionValue / price, maxPositionSize / price);

      case 'KELLY':
        // Simplified Kelly criterion (would need historical win rate and avg win/loss)
        const kellyPercent = 0.1; // 10% as default
        const positionValue2 = availableCash * kellyPercent;
        return Math.min(positionValue2 / price, maxPositionSize / price);

      default:
        return Math.min(1000 / price, availableCash / price); // Default to $1000 position
    }
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(
    trades: BacktestTrade[],
    equityCurve: EquityPoint[],
    initialCapital: number
  ): any {
    const finalCapital = equityCurve.length > 0 ? equityCurve[equityCurve.length - 1].equity : initialCapital;
    const totalReturn = finalCapital - initialCapital;
    const totalReturnPercent = (totalReturn / initialCapital) * 100;

    // Calculate max drawdown
    let maxDrawdown = 0;
    let maxDrawdownPercent = 0;
    for (const point of equityCurve) {
      if (point.drawdown > maxDrawdown) {
        maxDrawdown = point.drawdown;
        maxDrawdownPercent = (point.drawdown / point.equity) * 100;
      }
    }

    // Analyze trades
    const buyTrades = trades.filter(t => t.type === 'BUY');
    const sellTrades = trades.filter(t => t.type === 'SELL' && t.pnl !== undefined);

    const winningTrades = sellTrades.filter(t => (t.pnl || 0) > 0).length;
    const losingTrades = sellTrades.filter(t => (t.pnl || 0) < 0).length;
    const totalTrades = sellTrades.length;

    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

    const wins = sellTrades.filter(t => (t.pnl || 0) > 0).map(t => t.pnl || 0);
    const losses = sellTrades.filter(t => (t.pnl || 0) < 0).map(t => Math.abs(t.pnl || 0));

    const averageWin = wins.length > 0 ? wins.reduce((a, b) => a + b, 0) / wins.length : 0;
    const averageLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / losses.length : 0;

    const totalWins = wins.reduce((a, b) => a + b, 0);
    const totalLosses = losses.reduce((a, b) => a + b, 0);
    const profitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? Infinity : 0;

    // Calculate Sharpe ratio (simplified)
    const returns = equityCurve.map((point, i) => {
      if (i === 0) return 0;
      return (point.equity - equityCurve[i - 1].equity) / equityCurve[i - 1].equity;
    }).slice(1);

    const avgReturn = returns.length > 0 ? returns.reduce((a, b) => a + b, 0) / returns.length : 0;
    const returnStdDev = returns.length > 1 ? Math.sqrt(
      returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / (returns.length - 1)
    ) : 0;

    const sharpeRatio = returnStdDev > 0 ? (avgReturn / returnStdDev) * Math.sqrt(252) : 0; // Annualized

    return {
      finalCapital,
      totalReturn,
      totalReturnPercent,
      maxDrawdown,
      maxDrawdownPercent,
      sharpeRatio,
      winRate,
      totalTrades,
      winningTrades,
      losingTrades,
      averageWin,
      averageLoss,
      profitFactor
    };
  }
}

// Export singleton instance
export const backtestingEngine = BacktestingEngine.getInstance();
export default backtestingEngine;
