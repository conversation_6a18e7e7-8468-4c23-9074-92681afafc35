import { PrismaClient } from '../generated/prisma';

// Create a singleton instance of Prisma Client
class DatabaseService {
  private static instance: DatabaseService;
  public prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('📊 Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      console.log('📊 Database disconnected successfully');
    } catch (error) {
      console.error('❌ Database disconnection failed:', error);
      throw error;
    }
  }

  public async seedDatabase(): Promise<void> {
    try {
      // Create a default user
      const defaultUser = await this.prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: 'Default Trader',
        },
      });

      // Create a default portfolio
      await this.prisma.portfolio.upsert({
        where: { id: 'default-portfolio' },
        update: {},
        create: {
          id: 'default-portfolio',
          name: 'Main Portfolio',
          userId: defaultUser.id,
          totalValue: 245680,
          cash: 50000,
          dailyPnL: 3240,
          totalPnL: 45680,
        },
      });

      // Create some sample holdings
      const holdings = [
        {
          symbol: 'RELIANCE',
          quantity: 50,
          averagePrice: 2450,
          currentPrice: 2580,
          marketValue: 129000,
          unrealizedPnL: 6500,
          unrealizedPnLPercent: 5.3,
        },
        {
          symbol: 'TATAMOTORS',
          quantity: 100,
          averagePrice: 485,
          currentPrice: 465,
          marketValue: 46500,
          unrealizedPnL: -2000,
          unrealizedPnLPercent: -4.1,
        },
        {
          symbol: 'INFY',
          quantity: 75,
          averagePrice: 1620,
          currentPrice: 1720,
          marketValue: 129000,
          unrealizedPnL: 7500,
          unrealizedPnLPercent: 6.2,
        },
      ];

      for (const holding of holdings) {
        await this.prisma.holding.upsert({
          where: {
            portfolioId_symbol: {
              portfolioId: 'default-portfolio',
              symbol: holding.symbol,
            },
          },
          update: holding,
          create: {
            ...holding,
            portfolioId: 'default-portfolio',
          },
        });
      }

      // Create sample strategies
      const strategies = [
        {
          id: 'strategy-1',
          name: 'MACD Momentum',
          description: 'Buy when MACD crosses above signal line',
          asset: 'RELIANCE',
          status: 'ACTIVE' as const,
          pnl: 1250,
          parameters: {
            entryConditions: [
              {
                type: 'MACD_CROSS',
                parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
              },
            ],
            exitConditions: [
              { type: 'STOP_LOSS', parameters: { percentage: 5 } },
              { type: 'TAKE_PROFIT', parameters: { percentage: 15 } },
            ],
            riskManagement: {
              stopLossPercentage: 5,
              takeProfitPercentage: 15,
              maxDrawdownPercentage: 10,
              positionSizing: 'PERCENTAGE',
            },
            timeframe: '5m',
            maxPositionSize: 10000,
          },
          userId: defaultUser.id,
        },
        {
          id: 'strategy-2',
          name: 'RSI Oversold',
          description: 'Buy when RSI drops below 30',
          asset: 'TATAMOTORS',
          status: 'ACTIVE' as const,
          pnl: 850,
          parameters: {
            entryConditions: [
              {
                type: 'RSI_OVERSOLD',
                parameters: { period: 14, oversoldLevel: 30 },
              },
            ],
            exitConditions: [
              { type: 'RSI_OVERBOUGHT', parameters: { overboughtLevel: 70 } },
            ],
            riskManagement: {
              stopLossPercentage: 3,
              takeProfitPercentage: 10,
              maxDrawdownPercentage: 8,
              positionSizing: 'FIXED',
            },
            timeframe: '15m',
            maxPositionSize: 8000,
          },
          userId: defaultUser.id,
        },
      ];

      for (const strategy of strategies) {
        await this.prisma.strategy.upsert({
          where: { id: strategy.id },
          update: strategy,
          create: strategy,
        });
      }

      console.log('🌱 Database seeded successfully');
    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const db = DatabaseService.getInstance();
export default db;
