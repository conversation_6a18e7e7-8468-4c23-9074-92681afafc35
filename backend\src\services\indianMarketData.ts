import axios from 'axios';
import { MarketData } from '../types';
import db from './database';

interface IndianMarketDataset {
  metric: string;
  label: string;
  values: Array<[string, string | number, any?]>;
  meta: {
    is_weekly?: boolean;
  };
}

interface IndianMarketResponse {
  datasets: IndianMarketDataset[];
}

class IndianMarketDataService {
  private static instance: IndianMarketDataService;
  private baseUrl: string;

  private constructor() {
    // Use the actual Indian market data API URL from environment
    this.baseUrl = process.env.INDIAN_MARKET_API_URL || 'https://stock.indianapi.in';
    console.log(`🇮🇳 Indian Market API configured: ${this.baseUrl}`);
    console.log(`🔧 Using real API: ${process.env.USE_REAL_INDIAN_API === 'true' ? 'YES' : 'NO (Mock data)'}`);
    console.log(`🔑 API Key configured: ${process.env.INDIAN_MARKET_API_KEY ? 'YES' : 'NO'}`);
  }

  public static getInstance(): IndianMarketDataService {
    if (!IndianMarketDataService.instance) {
      IndianMarketDataService.instance = new IndianMarketDataService();
    }
    return IndianMarketDataService.instance;
  }

  /**
   * Fetch historical data from Indian market API
   */
  public async getHistoricalData(
    symbol: string,
    period: '1m' | '6m' | '1yr' | '3yr' | '5yr' | '10yr' | 'max' = '5yr',
    filter: 'default' | 'price' | 'pe' | 'sm' | 'evebitda' | 'ptb' | 'mcs' = 'price'
  ): Promise<MarketData[]> {
    // Check if we should use real API or mock data
    const useRealAPI = process.env.USE_REAL_INDIAN_API === 'true';

    if (!useRealAPI) {
      console.log(`🔧 Using mock data for ${symbol} (USE_REAL_INDIAN_API=false)`);
      return this.generateMockData(symbol, period);
    }

    try {
      console.log(`📈 Fetching real Indian market data for ${symbol} (${period}) from ${this.baseUrl}`);

      // Try to fetch from the real Indian market API
      const headers: any = {
        'Accept': 'application/json',
        'User-Agent': 'Kuber.ai/1.0',
        'Content-Type': 'application/json'
      };

      // Add API key if available
      if (process.env.INDIAN_MARKET_API_KEY) {
        headers['X-Api-Key'] = process.env.INDIAN_MARKET_API_KEY;
      }

      const response = await axios.get<IndianMarketResponse>(`${this.baseUrl}/historical_data`, {
        params: {
          stock_name: symbol,
          period,
          filter
        },
        timeout: 30000,
        headers
      });

      console.log(`📊 API Response status: ${response.status}`);

      if (!response.data || !response.data.datasets) {
        throw new Error('Invalid API response format');
      }

      const marketData = this.parseIndianMarketData(symbol, response.data);

      // Store in database for future use
      if (marketData.length > 0) {
        await this.storeHistoricalData(marketData);
        console.log(`💾 Stored ${marketData.length} data points in database`);
      }

      console.log(`✅ Successfully fetched ${marketData.length} real data points for ${symbol}`);
      return marketData;

    } catch (error: any) {
      console.error(`❌ Error fetching real Indian market data for ${symbol}:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url
      });

      // Fallback strategy: database -> mock data
      console.log(`🔄 Attempting fallback strategies for ${symbol}`);

      try {
        // Try to get data from database first
        const fallbackData = await this.getStoredHistoricalData(symbol, this.getPeriodDates(period));

        if (fallbackData.length > 0) {
          console.log(`✅ Using ${fallbackData.length} cached data points for ${symbol}`);
          return fallbackData;
        }
      } catch (dbError) {
        console.error('Database fallback also failed:', dbError);
      }

      // Final fallback: generate mock data
      console.log(`🔄 Generating mock data for ${symbol} as final fallback`);
      return this.generateMockData(symbol, period);
    }
  }

  /**
   * Parse Indian market API response to MarketData format
   * Handles the exact format: datasets[].values = [["date", "price"], ...]
   */
  private parseIndianMarketData(symbol: string, data: IndianMarketResponse): MarketData[] {
    console.log(`🔍 Parsing Indian market data for ${symbol}, found ${data.datasets.length} datasets`);

    const priceDataset = data.datasets.find(d => d.metric === 'Price');
    const volumeDataset = data.datasets.find(d => d.metric === 'Volume');
    const dma50Dataset = data.datasets.find(d => d.metric === 'DMA50');
    const dma200Dataset = data.datasets.find(d => d.metric === 'DMA200');

    if (!priceDataset || !priceDataset.values || priceDataset.values.length === 0) {
      throw new Error('No price data found in API response');
    }

    console.log(`📊 Processing ${priceDataset.values.length} price data points`);
    const marketData: MarketData[] = [];

    for (let i = 0; i < priceDataset.values.length; i++) {
      try {
        const priceEntry = priceDataset.values[i];
        const volumeEntry = volumeDataset?.values[i];

        // Parse date and price from the API format: ["2024-06-27", "3934.15"]
        const dateStr = priceEntry[0] as string;
        const priceStr = priceEntry[1] as string;

        const date = new Date(dateStr);
        const price = parseFloat(priceStr);

        // Validate data
        if (isNaN(date.getTime()) || isNaN(price) || price <= 0) {
          console.warn(`⚠️ Skipping invalid data point: date=${dateStr}, price=${priceStr}`);
          continue;
        }

        // Parse volume - format: ["2024-06-27", volume_number, {delivery: ...}]
        let volume = 0;
        if (volumeEntry && volumeEntry.length >= 2) {
          const volumeValue = volumeEntry[1];
          volume = typeof volumeValue === 'number' ? volumeValue : parseInt(volumeValue as string) || 0;
        }

        // For Indian market data, we typically only get close prices
        // We'll estimate OHLC based on close price with small variations
        const closePrice = price;
        const dailyVariation = closePrice * 0.005; // 0.5% variation

        const open = closePrice + (Math.random() - 0.5) * dailyVariation;
        const high = Math.max(open, closePrice) + Math.random() * dailyVariation;
        const low = Math.min(open, closePrice) - Math.random() * dailyVariation;

        marketData.push({
          symbol,
          timestamp: date,
          open: Math.round(open * 100) / 100,
          high: Math.round(high * 100) / 100,
          low: Math.round(low * 100) / 100,
          close: closePrice,
          volume: Math.max(volume, 0),
          adjustedClose: closePrice
        });

      } catch (error) {
        console.error(`❌ Error parsing data point ${i}:`, error);
        continue;
      }
    }

    console.log(`✅ Successfully parsed ${marketData.length} data points for ${symbol}`);
    return marketData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get date range for period
   */
  private getPeriodDates(period: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '1m':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '6m':
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case '1yr':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case '3yr':
        startDate.setFullYear(endDate.getFullYear() - 3);
        break;
      case '5yr':
        startDate.setFullYear(endDate.getFullYear() - 5);
        break;
      case '10yr':
        startDate.setFullYear(endDate.getFullYear() - 10);
        break;
      case 'max':
        startDate.setFullYear(endDate.getFullYear() - 20);
        break;
      default:
        startDate.setFullYear(endDate.getFullYear() - 5);
    }

    return { startDate, endDate };
  }

  /**
   * Store historical data in database
   */
  private async storeHistoricalData(marketData: MarketData[]): Promise<void> {
    try {
      for (const data of marketData) {
        await db.prisma.marketData.upsert({
          where: {
            symbol_timestamp: {
              symbol: data.symbol,
              timestamp: data.timestamp,
            },
          },
          update: {
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
          create: {
            symbol: data.symbol,
            timestamp: data.timestamp,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
        });
      }
      console.log(`💾 Stored ${marketData.length} Indian market data points for ${marketData[0]?.symbol}`);
    } catch (error) {
      console.error('❌ Error storing Indian market data:', error);
      throw error;
    }
  }

  /**
   * Get stored historical data from database
   */
  private async getStoredHistoricalData(
    symbol: string,
    dateRange: { startDate: Date; endDate: Date }
  ): Promise<MarketData[]> {
    try {
      const data = await db.prisma.marketData.findMany({
        where: {
          symbol,
          timestamp: {
            gte: dateRange.startDate,
            lte: dateRange.endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });

      return data.map(item => ({
        symbol: item.symbol,
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjustedClose,
      }));
    } catch (error) {
      console.error(`❌ Error fetching stored Indian market data for ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Test connection to the real Indian market API
   */
  public async testAPIConnection(): Promise<{ success: boolean; message: string; data?: any }> {
    const useRealAPI = process.env.USE_REAL_INDIAN_API === 'true';

    if (!useRealAPI) {
      return {
        success: true,
        message: 'Using mock data (USE_REAL_INDIAN_API=false)',
        data: { mode: 'mock', baseUrl: this.baseUrl }
      };
    }

    try {
      console.log(`🧪 Testing connection to Indian Market API: ${this.baseUrl}`);

      // Prepare headers with API key
      const headers: any = {
        'Accept': 'application/json',
        'User-Agent': 'Kuber.ai/1.0'
      };

      if (process.env.INDIAN_MARKET_API_KEY) {
        headers['X-Api-Key'] = process.env.INDIAN_MARKET_API_KEY;
      }

      // Test with a common Indian stock
      const response = await axios.get(`${this.baseUrl}/historical_data`, {
        params: {
          stock_name: 'RELIANCE',
          period: '1m',
          filter: 'default'
        },
        timeout: 10000,
        headers
      });

      return {
        success: true,
        message: 'Successfully connected to Indian Market API',
        data: {
          status: response.status,
          datasetCount: response.data?.datasets?.length || 0,
          sampleData: response.data?.datasets?.[0]?.values?.slice(0, 2) || []
        }
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Failed to connect to Indian Market API: ${error.message}`,
        data: {
          error: error.message,
          status: error.response?.status,
          url: error.config?.url
        }
      };
    }
  }

  /**
   * Get current price for Indian stock
   */
  public async getCurrentPrice(symbol: string): Promise<{ price: number; change: number; changePercent: number }> {
    const useRealAPI = process.env.USE_REAL_INDIAN_API === 'true';

    if (!useRealAPI) {
      // Use mock data
      const basePrices: { [key: string]: number } = {
        'RELIANCE': 2580, 'TCS': 3420, 'HDFCBANK': 1720, 'INFY': 1465,
        'ICICIBANK': 945, 'SBIN': 565, 'BHARTIARTL': 825, 'ITC': 465,
        'WIPRO': 415, 'MARUTI': 9850, 'BAJFINANCE': 6750, 'HCLTECH': 1245,
        'TATAMOTORS': 485, 'HINDUNILVR': 2450, 'KOTAKBANK': 1820
      };

      const basePrice = basePrices[symbol] || 1000;
      const change = (Math.random() - 0.5) * 100;
      const changePercent = (change / basePrice) * 100;

      return {
        price: Math.round((basePrice + change) * 100) / 100,
        change: Math.round(change * 100) / 100,
        changePercent: Math.round(changePercent * 100) / 100
      };
    }

    try {
      // Try to get latest price from recent historical data
      const recentData = await this.getHistoricalData(symbol, '1m', 'price');

      if (recentData.length === 0) {
        throw new Error(`No recent data found for ${symbol}`);
      }

      const latestData = recentData[recentData.length - 1];
      const previousData = recentData[recentData.length - 2];

      let change = 0;
      let changePercent = 0;

      if (previousData) {
        change = latestData.close - previousData.close;
        changePercent = (change / previousData.close) * 100;
      }

      return {
        price: latestData.close,
        change: Math.round(change * 100) / 100,
        changePercent: Math.round(changePercent * 100) / 100
      };

    } catch (error) {
      console.error(`❌ Error fetching current price for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Generate mock data for development when API is not available
   */
  private generateMockData(symbol: string, period: string): MarketData[] {
    const { startDate, endDate } = this.getPeriodDates(period);
    const data: MarketData[] = [];

    // Base prices for different stocks
    const basePrices: { [key: string]: number } = {
      'RELIANCE': 2500,
      'TCS': 3200,
      'HDFCBANK': 1600,
      'INFY': 1400,
      'ICICIBANK': 900,
      'SBIN': 550,
      'BHARTIARTL': 800,
      'ITC': 450,
      'WIPRO': 400,
      'MARUTI': 9500,
      'BAJFINANCE': 6500,
      'HCLTECH': 1200,
      'TATAMOTORS': 500,
      'HINDUNILVR': 2400,
      'KOTAKBANK': 1800
    };

    let basePrice = basePrices[symbol] || 1000;
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      // Generate realistic price movement
      const volatility = 0.02; // 2% daily volatility
      const trend = 0.0002; // Slight upward trend
      const randomChange = (Math.random() - 0.5) * volatility;

      basePrice = basePrice * (1 + trend + randomChange);

      const open = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const close = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const high = Math.max(open, close) * (1 + Math.random() * 0.02);
      const low = Math.min(open, close) * (1 - Math.random() * 0.02);
      const volume = Math.floor(Math.random() * 1000000) + 100000;

      data.push({
        symbol,
        timestamp: new Date(currentDate),
        open: Math.round(open * 100) / 100,
        high: Math.round(high * 100) / 100,
        low: Math.round(low * 100) / 100,
        close: Math.round(close * 100) / 100,
        volume,
        adjustedClose: Math.round(close * 100) / 100
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);

      // Skip weekends
      if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
        currentDate.setDate(currentDate.getDate() + (currentDate.getDay() === 0 ? 1 : 2));
      }
    }

    console.log(`🔧 Generated ${data.length} mock data points for ${symbol}`);
    return data;
  }

  /**
   * Search for Indian stock symbols
   */
  public async searchSymbols(query: string): Promise<Array<{ symbol: string; name: string; exchange: string }>> {
    // Enhanced stock database with more Indian stocks
    const indianStocks = [
      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', exchange: 'NSE' },
      { symbol: 'TCS', name: 'Tata Consultancy Services Limited', exchange: 'NSE' },
      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', exchange: 'NSE' },
      { symbol: 'INFY', name: 'Infosys Limited', exchange: 'NSE' },
      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', exchange: 'NSE' },
      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', exchange: 'NSE' },
      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Limited', exchange: 'NSE' },
      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', exchange: 'NSE' },
      { symbol: 'ITC', name: 'ITC Limited', exchange: 'NSE' },
      { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE' },
      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', exchange: 'NSE' },
      { symbol: 'WIPRO', name: 'Wipro Limited', exchange: 'NSE' },
      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', exchange: 'NSE' },
      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', exchange: 'NSE' },
      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', exchange: 'NSE' },
      { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', exchange: 'NSE' },
      { symbol: 'NESTLEIND', name: 'Nestle India Limited', exchange: 'NSE' },
      { symbol: 'LTIM', name: 'LTIMindtree Limited', exchange: 'NSE' },
      { symbol: 'AXISBANK', name: 'Axis Bank Limited', exchange: 'NSE' },
      { symbol: 'TITAN', name: 'Titan Company Limited', exchange: 'NSE' },
      { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries Limited', exchange: 'NSE' },
      { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', exchange: 'NSE' },
      { symbol: 'ONGC', name: 'Oil and Natural Gas Corporation Limited', exchange: 'NSE' },
      { symbol: 'NTPC', name: 'NTPC Limited', exchange: 'NSE' },
      { symbol: 'POWERGRID', name: 'Power Grid Corporation of India Limited', exchange: 'NSE' }
    ];

    return indianStocks.filter(stock =>
      stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
      stock.name.toLowerCase().includes(query.toLowerCase())
    );
  }
}

// Export singleton instance
export const indianMarketDataService = IndianMarketDataService.getInstance();
export default indianMarketDataService;
