import axios from 'axios';
import { MarketData } from '../types';
import db from './database';

interface IndianMarketDataset {
  metric: string;
  label: string;
  values: Array<[string, string | number, any?]>;
  meta: {
    is_weekly?: boolean;
  };
}

interface IndianMarketResponse {
  datasets: IndianMarketDataset[];
}

class IndianMarketDataService {
  private static instance: IndianMarketDataService;
  private baseUrl: string;

  private constructor() {
    // Use the actual Indian market data API URL from environment
    this.baseUrl = process.env.INDIAN_MARKET_API_URL || 'https://api.example-indian-market.com';
  }

  public static getInstance(): IndianMarketDataService {
    if (!IndianMarketDataService.instance) {
      IndianMarketDataService.instance = new IndianMarketDataService();
    }
    return IndianMarketDataService.instance;
  }

  /**
   * Fetch historical data from Indian market API
   */
  public async getHistoricalData(
    symbol: string,
    period: '1m' | '6m' | '1yr' | '3yr' | '5yr' | '10yr' | 'max' = '5yr',
    filter: 'default' | 'price' | 'pe' | 'sm' | 'evebitda' | 'ptb' | 'mcs' = 'price'
  ): Promise<MarketData[]> {
    try {
      console.log(`📈 Fetching Indian market data for ${symbol} (${period})`);

      // Try to fetch from the real Indian market API
      const response = await axios.get<IndianMarketResponse>(`${this.baseUrl}/historical_data`, {
        params: {
          stock_name: symbol,
          period,
          filter
        },
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Kuber.ai/1.0'
        }
      });

      const marketData = this.parseIndianMarketData(symbol, response.data);

      // Store in database
      if (marketData.length > 0) {
        await this.storeHistoricalData(marketData);
      }

      console.log(`✅ Successfully fetched ${marketData.length} data points for ${symbol}`);
      return marketData;
    } catch (error) {
      console.error(`❌ Error fetching Indian market data for ${symbol}:`, error);

      // If API fails, try to get data from database
      console.log(`🔄 Falling back to database data for ${symbol}`);
      const fallbackData = await this.getStoredHistoricalData(symbol, this.getPeriodDates(period));

      // If no database data, generate mock data for development
      if (fallbackData.length === 0) {
        console.log(`🔄 Generating mock data for ${symbol} for development`);
        return this.generateMockData(symbol, period);
      }

      return fallbackData;
    }
  }

  /**
   * Parse Indian market API response to MarketData format
   */
  private parseIndianMarketData(symbol: string, data: IndianMarketResponse): MarketData[] {
    const priceDataset = data.datasets.find(d => d.metric === 'Price');
    const volumeDataset = data.datasets.find(d => d.metric === 'Volume');
    
    if (!priceDataset) {
      throw new Error('No price data found in response');
    }

    const marketData: MarketData[] = [];
    
    for (let i = 0; i < priceDataset.values.length; i++) {
      const priceEntry = priceDataset.values[i];
      const volumeEntry = volumeDataset?.values[i];
      
      const date = new Date(priceEntry[0] as string);
      const price = parseFloat(priceEntry[1] as string);
      const volume = volumeEntry ? (volumeEntry[1] as number) : 0;
      
      // For Indian market data, we might not have OHLC, so we'll use close price for all
      marketData.push({
        symbol,
        timestamp: date,
        open: price, // Using close as open (limitation of the API)
        high: price, // Using close as high (limitation of the API)
        low: price,  // Using close as low (limitation of the API)
        close: price,
        volume,
        adjustedClose: price
      });
    }

    return marketData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get date range for period
   */
  private getPeriodDates(period: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '1m':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '6m':
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case '1yr':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case '3yr':
        startDate.setFullYear(endDate.getFullYear() - 3);
        break;
      case '5yr':
        startDate.setFullYear(endDate.getFullYear() - 5);
        break;
      case '10yr':
        startDate.setFullYear(endDate.getFullYear() - 10);
        break;
      case 'max':
        startDate.setFullYear(endDate.getFullYear() - 20);
        break;
      default:
        startDate.setFullYear(endDate.getFullYear() - 5);
    }

    return { startDate, endDate };
  }

  /**
   * Store historical data in database
   */
  private async storeHistoricalData(marketData: MarketData[]): Promise<void> {
    try {
      for (const data of marketData) {
        await db.prisma.marketData.upsert({
          where: {
            symbol_timestamp: {
              symbol: data.symbol,
              timestamp: data.timestamp,
            },
          },
          update: {
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
          create: {
            symbol: data.symbol,
            timestamp: data.timestamp,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
        });
      }
      console.log(`💾 Stored ${marketData.length} Indian market data points for ${marketData[0]?.symbol}`);
    } catch (error) {
      console.error('❌ Error storing Indian market data:', error);
      throw error;
    }
  }

  /**
   * Get stored historical data from database
   */
  private async getStoredHistoricalData(
    symbol: string,
    dateRange: { startDate: Date; endDate: Date }
  ): Promise<MarketData[]> {
    try {
      const data = await db.prisma.marketData.findMany({
        where: {
          symbol,
          timestamp: {
            gte: dateRange.startDate,
            lte: dateRange.endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });

      return data.map(item => ({
        symbol: item.symbol,
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjustedClose,
      }));
    } catch (error) {
      console.error(`❌ Error fetching stored Indian market data for ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Get current price for Indian stock (mock implementation)
   */
  public async getCurrentPrice(symbol: string): Promise<{ price: number; change: number; changePercent: number }> {
    try {
      // In a real implementation, you'd call a real-time price API
      // For now, we'll get the latest price from historical data
      const latestData = await db.prisma.marketData.findFirst({
        where: { symbol },
        orderBy: { timestamp: 'desc' }
      });

      if (!latestData) {
        throw new Error(`No price data found for ${symbol}`);
      }

      // Mock change calculation (in real implementation, you'd compare with previous day)
      const change = Math.random() * 100 - 50; // Random change between -50 and +50
      const changePercent = (change / latestData.close) * 100;

      return {
        price: latestData.close,
        change,
        changePercent
      };
    } catch (error) {
      console.error(`❌ Error fetching current price for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Generate mock data for development when API is not available
   */
  private generateMockData(symbol: string, period: string): MarketData[] {
    const { startDate, endDate } = this.getPeriodDates(period);
    const data: MarketData[] = [];

    // Base prices for different stocks
    const basePrices: { [key: string]: number } = {
      'RELIANCE': 2500,
      'TCS': 3200,
      'HDFCBANK': 1600,
      'INFY': 1400,
      'ICICIBANK': 900,
      'SBIN': 550,
      'BHARTIARTL': 800,
      'ITC': 450,
      'WIPRO': 400,
      'MARUTI': 9500,
      'BAJFINANCE': 6500,
      'HCLTECH': 1200,
      'TATAMOTORS': 500,
      'HINDUNILVR': 2400,
      'KOTAKBANK': 1800
    };

    let basePrice = basePrices[symbol] || 1000;
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      // Generate realistic price movement
      const volatility = 0.02; // 2% daily volatility
      const trend = 0.0002; // Slight upward trend
      const randomChange = (Math.random() - 0.5) * volatility;

      basePrice = basePrice * (1 + trend + randomChange);

      const open = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const close = basePrice * (1 + (Math.random() - 0.5) * 0.01);
      const high = Math.max(open, close) * (1 + Math.random() * 0.02);
      const low = Math.min(open, close) * (1 - Math.random() * 0.02);
      const volume = Math.floor(Math.random() * 1000000) + 100000;

      data.push({
        symbol,
        timestamp: new Date(currentDate),
        open: Math.round(open * 100) / 100,
        high: Math.round(high * 100) / 100,
        low: Math.round(low * 100) / 100,
        close: Math.round(close * 100) / 100,
        volume,
        adjustedClose: Math.round(close * 100) / 100
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);

      // Skip weekends
      if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
        currentDate.setDate(currentDate.getDate() + (currentDate.getDay() === 0 ? 1 : 2));
      }
    }

    console.log(`🔧 Generated ${data.length} mock data points for ${symbol}`);
    return data;
  }

  /**
   * Search for Indian stock symbols
   */
  public async searchSymbols(query: string): Promise<Array<{ symbol: string; name: string; exchange: string }>> {
    // Enhanced stock database with more Indian stocks
    const indianStocks = [
      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', exchange: 'NSE' },
      { symbol: 'TCS', name: 'Tata Consultancy Services Limited', exchange: 'NSE' },
      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', exchange: 'NSE' },
      { symbol: 'INFY', name: 'Infosys Limited', exchange: 'NSE' },
      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', exchange: 'NSE' },
      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', exchange: 'NSE' },
      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Limited', exchange: 'NSE' },
      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', exchange: 'NSE' },
      { symbol: 'ITC', name: 'ITC Limited', exchange: 'NSE' },
      { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE' },
      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', exchange: 'NSE' },
      { symbol: 'WIPRO', name: 'Wipro Limited', exchange: 'NSE' },
      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', exchange: 'NSE' },
      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', exchange: 'NSE' },
      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', exchange: 'NSE' },
      { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', exchange: 'NSE' },
      { symbol: 'NESTLEIND', name: 'Nestle India Limited', exchange: 'NSE' },
      { symbol: 'LTIM', name: 'LTIMindtree Limited', exchange: 'NSE' },
      { symbol: 'AXISBANK', name: 'Axis Bank Limited', exchange: 'NSE' },
      { symbol: 'TITAN', name: 'Titan Company Limited', exchange: 'NSE' },
      { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries Limited', exchange: 'NSE' },
      { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', exchange: 'NSE' },
      { symbol: 'ONGC', name: 'Oil and Natural Gas Corporation Limited', exchange: 'NSE' },
      { symbol: 'NTPC', name: 'NTPC Limited', exchange: 'NSE' },
      { symbol: 'POWERGRID', name: 'Power Grid Corporation of India Limited', exchange: 'NSE' }
    ];

    return indianStocks.filter(stock =>
      stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
      stock.name.toLowerCase().includes(query.toLowerCase())
    );
  }
}

// Export singleton instance
export const indianMarketDataService = IndianMarketDataService.getInstance();
export default indianMarketDataService;
