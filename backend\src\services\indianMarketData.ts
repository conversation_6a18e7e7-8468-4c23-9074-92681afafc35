import axios from 'axios';
import { MarketData } from '../types';
import db from './database';

interface IndianMarketDataset {
  metric: string;
  label: string;
  values: Array<[string, string | number, any?]>;
  meta: {
    is_weekly?: boolean;
  };
}

interface IndianMarketResponse {
  datasets: IndianMarketDataset[];
}

class IndianMarketDataService {
  private static instance: IndianMarketDataService;
  private baseUrl: string;

  private constructor() {
    // You'll need to replace this with the actual Indian market data API URL
    this.baseUrl = process.env.INDIAN_MARKET_API_URL || 'https://api.indianmarket.com';
  }

  public static getInstance(): IndianMarketDataService {
    if (!IndianMarketDataService.instance) {
      IndianMarketDataService.instance = new IndianMarketDataService();
    }
    return IndianMarketDataService.instance;
  }

  /**
   * Fetch historical data from Indian market API
   */
  public async getHistoricalData(
    symbol: string,
    period: '1m' | '6m' | '1yr' | '3yr' | '5yr' | '10yr' | 'max' = '5yr',
    filter: 'default' | 'price' | 'pe' | 'sm' | 'evebitda' | 'ptb' | 'mcs' = 'price'
  ): Promise<MarketData[]> {
    try {
      console.log(`📈 Fetching Indian market data for ${symbol} (${period})`);
      
      const response = await axios.get<IndianMarketResponse>(`${this.baseUrl}/historical_data`, {
        params: {
          stock_name: symbol,
          period,
          filter
        },
        timeout: 30000
      });

      const marketData = this.parseIndianMarketData(symbol, response.data);
      
      // Store in database
      if (marketData.length > 0) {
        await this.storeHistoricalData(marketData);
      }

      return marketData;
    } catch (error) {
      console.error(`❌ Error fetching Indian market data for ${symbol}:`, error);
      
      // If API fails, try to get data from database
      console.log(`🔄 Falling back to database data for ${symbol}`);
      return this.getStoredHistoricalData(symbol, this.getPeriodDates(period));
    }
  }

  /**
   * Parse Indian market API response to MarketData format
   */
  private parseIndianMarketData(symbol: string, data: IndianMarketResponse): MarketData[] {
    const priceDataset = data.datasets.find(d => d.metric === 'Price');
    const volumeDataset = data.datasets.find(d => d.metric === 'Volume');
    
    if (!priceDataset) {
      throw new Error('No price data found in response');
    }

    const marketData: MarketData[] = [];
    
    for (let i = 0; i < priceDataset.values.length; i++) {
      const priceEntry = priceDataset.values[i];
      const volumeEntry = volumeDataset?.values[i];
      
      const date = new Date(priceEntry[0] as string);
      const price = parseFloat(priceEntry[1] as string);
      const volume = volumeEntry ? (volumeEntry[1] as number) : 0;
      
      // For Indian market data, we might not have OHLC, so we'll use close price for all
      marketData.push({
        symbol,
        timestamp: date,
        open: price, // Using close as open (limitation of the API)
        high: price, // Using close as high (limitation of the API)
        low: price,  // Using close as low (limitation of the API)
        close: price,
        volume,
        adjustedClose: price
      });
    }

    return marketData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get date range for period
   */
  private getPeriodDates(period: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '1m':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case '6m':
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case '1yr':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case '3yr':
        startDate.setFullYear(endDate.getFullYear() - 3);
        break;
      case '5yr':
        startDate.setFullYear(endDate.getFullYear() - 5);
        break;
      case '10yr':
        startDate.setFullYear(endDate.getFullYear() - 10);
        break;
      case 'max':
        startDate.setFullYear(endDate.getFullYear() - 20);
        break;
      default:
        startDate.setFullYear(endDate.getFullYear() - 5);
    }

    return { startDate, endDate };
  }

  /**
   * Store historical data in database
   */
  private async storeHistoricalData(marketData: MarketData[]): Promise<void> {
    try {
      for (const data of marketData) {
        await db.prisma.marketData.upsert({
          where: {
            symbol_timestamp: {
              symbol: data.symbol,
              timestamp: data.timestamp,
            },
          },
          update: {
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
          create: {
            symbol: data.symbol,
            timestamp: data.timestamp,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
        });
      }
      console.log(`💾 Stored ${marketData.length} Indian market data points for ${marketData[0]?.symbol}`);
    } catch (error) {
      console.error('❌ Error storing Indian market data:', error);
      throw error;
    }
  }

  /**
   * Get stored historical data from database
   */
  private async getStoredHistoricalData(
    symbol: string,
    dateRange: { startDate: Date; endDate: Date }
  ): Promise<MarketData[]> {
    try {
      const data = await db.prisma.marketData.findMany({
        where: {
          symbol,
          timestamp: {
            gte: dateRange.startDate,
            lte: dateRange.endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });

      return data.map(item => ({
        symbol: item.symbol,
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjustedClose,
      }));
    } catch (error) {
      console.error(`❌ Error fetching stored Indian market data for ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Get current price for Indian stock (mock implementation)
   */
  public async getCurrentPrice(symbol: string): Promise<{ price: number; change: number; changePercent: number }> {
    try {
      // In a real implementation, you'd call a real-time price API
      // For now, we'll get the latest price from historical data
      const latestData = await db.prisma.marketData.findFirst({
        where: { symbol },
        orderBy: { timestamp: 'desc' }
      });

      if (!latestData) {
        throw new Error(`No price data found for ${symbol}`);
      }

      // Mock change calculation (in real implementation, you'd compare with previous day)
      const change = Math.random() * 100 - 50; // Random change between -50 and +50
      const changePercent = (change / latestData.close) * 100;

      return {
        price: latestData.close,
        change,
        changePercent
      };
    } catch (error) {
      console.error(`❌ Error fetching current price for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Search for Indian stock symbols
   */
  public async searchSymbols(query: string): Promise<Array<{ symbol: string; name: string; exchange: string }>> {
    // Mock implementation - in reality, you'd call a symbol search API
    const mockSymbols = [
      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', exchange: 'NSE' },
      { symbol: 'TCS', name: 'Tata Consultancy Services Limited', exchange: 'NSE' },
      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', exchange: 'NSE' },
      { symbol: 'INFY', name: 'Infosys Limited', exchange: 'NSE' },
      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', exchange: 'NSE' },
      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', exchange: 'NSE' },
      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Limited', exchange: 'NSE' },
      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', exchange: 'NSE' },
      { symbol: 'ITC', name: 'ITC Limited', exchange: 'NSE' },
      { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE' },
      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', exchange: 'NSE' },
      { symbol: 'WIPRO', name: 'Wipro Limited', exchange: 'NSE' },
      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', exchange: 'NSE' },
      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', exchange: 'NSE' },
      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', exchange: 'NSE' }
    ];

    return mockSymbols.filter(stock => 
      stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
      stock.name.toLowerCase().includes(query.toLowerCase())
    );
  }
}

// Export singleton instance
export const indianMarketDataService = IndianMarketDataService.getInstance();
export default indianMarketDataService;
