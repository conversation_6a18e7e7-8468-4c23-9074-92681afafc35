import yahooFinance from 'yahoo-finance2';
import { MarketData, RealTimePrice } from '../types';
import db from './database';
import { io } from '../index';

class MarketDataService {
  private static instance: MarketDataService;
  private priceUpdateInterval: NodeJS.Timeout | null = null;
  private subscribedSymbols: Set<string> = new Set();

  private constructor() {}

  public static getInstance(): MarketDataService {
    if (!MarketDataService.instance) {
      MarketDataService.instance = new MarketDataService();
    }
    return MarketDataService.instance;
  }

  /**
   * Fetch historical data for a symbol
   */
  public async getHistoricalData(
    symbol: string,
    startDate: Date,
    endDate: Date,
    interval: '1d' | '1wk' | '1mo' = '1d'
  ): Promise<MarketData[]> {
    try {
      console.log(`📈 Fetching historical data for ${symbol} from ${startDate.toISOString()} to ${endDate.toISOString()}`);
      
      const result = await yahooFinance.historical(symbol, {
        period1: startDate,
        period2: endDate,
        interval: interval,
      });

      const marketData: MarketData[] = result.map((item) => ({
        symbol,
        timestamp: item.date,
        open: item.open || 0,
        high: item.high || 0,
        low: item.low || 0,
        close: item.close || 0,
        volume: item.volume || 0,
        adjustedClose: item.adjClose,
      }));

      // Store in database
      await this.storeHistoricalData(marketData);

      return marketData;
    } catch (error) {
      console.error(`❌ Error fetching historical data for ${symbol}:`, error);
      throw new Error(`Failed to fetch historical data for ${symbol}`);
    }
  }

  /**
   * Get current price for a symbol
   */
  public async getCurrentPrice(symbol: string): Promise<RealTimePrice> {
    try {
      const quote = await yahooFinance.quote(symbol);
      
      if (!quote) {
        throw new Error(`No quote data found for ${symbol}`);
      }

      const realTimePrice: RealTimePrice = {
        symbol,
        price: quote.regularMarketPrice || 0,
        change: quote.regularMarketChange || 0,
        changePercent: quote.regularMarketChangePercent || 0,
        timestamp: new Date(),
        volume: quote.regularMarketVolume || 0,
      };

      return realTimePrice;
    } catch (error) {
      console.error(`❌ Error fetching current price for ${symbol}:`, error);
      throw new Error(`Failed to fetch current price for ${symbol}`);
    }
  }

  /**
   * Get multiple current prices
   */
  public async getCurrentPrices(symbols: string[]): Promise<RealTimePrice[]> {
    try {
      const promises = symbols.map(symbol => this.getCurrentPrice(symbol));
      const results = await Promise.allSettled(promises);
      
      return results
        .filter((result): result is PromiseFulfilledResult<RealTimePrice> => result.status === 'fulfilled')
        .map(result => result.value);
    } catch (error) {
      console.error('❌ Error fetching multiple current prices:', error);
      throw new Error('Failed to fetch current prices');
    }
  }

  /**
   * Store historical data in database
   */
  private async storeHistoricalData(marketData: MarketData[]): Promise<void> {
    try {
      for (const data of marketData) {
        await db.prisma.marketData.upsert({
          where: {
            symbol_timestamp: {
              symbol: data.symbol,
              timestamp: data.timestamp,
            },
          },
          update: {
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
          create: {
            symbol: data.symbol,
            timestamp: data.timestamp,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            volume: data.volume,
            adjustedClose: data.adjustedClose,
          },
        });
      }
      console.log(`💾 Stored ${marketData.length} historical data points for ${marketData[0]?.symbol}`);
    } catch (error) {
      console.error('❌ Error storing historical data:', error);
      throw error;
    }
  }

  /**
   * Get historical data from database
   */
  public async getStoredHistoricalData(
    symbol: string,
    startDate: Date,
    endDate: Date
  ): Promise<MarketData[]> {
    try {
      const data = await db.prisma.marketData.findMany({
        where: {
          symbol,
          timestamp: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });

      return data.map(item => ({
        symbol: item.symbol,
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjustedClose,
      }));
    } catch (error) {
      console.error(`❌ Error fetching stored historical data for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Start real-time price updates
   */
  public startRealTimeUpdates(): void {
    if (this.priceUpdateInterval) {
      return; // Already running
    }

    this.priceUpdateInterval = setInterval(async () => {
      if (this.subscribedSymbols.size === 0) {
        return;
      }

      try {
        const symbols = Array.from(this.subscribedSymbols);
        const prices = await this.getCurrentPrices(symbols);
        
        // Emit price updates to connected clients
        prices.forEach(price => {
          io.to(`price-${price.symbol}`).emit('price-update', price);
        });
        
        console.log(`📊 Updated prices for ${prices.length} symbols`);
      } catch (error) {
        console.error('❌ Error in real-time price updates:', error);
      }
    }, 5000); // Update every 5 seconds

    console.log('🔄 Started real-time price updates');
  }

  /**
   * Stop real-time price updates
   */
  public stopRealTimeUpdates(): void {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
      this.priceUpdateInterval = null;
      console.log('⏹️ Stopped real-time price updates');
    }
  }

  /**
   * Subscribe to symbol for real-time updates
   */
  public subscribeToSymbol(symbol: string): void {
    this.subscribedSymbols.add(symbol);
    console.log(`📈 Subscribed to ${symbol} for real-time updates`);
    
    // Start updates if this is the first subscription
    if (this.subscribedSymbols.size === 1) {
      this.startRealTimeUpdates();
    }
  }

  /**
   * Unsubscribe from symbol
   */
  public unsubscribeFromSymbol(symbol: string): void {
    this.subscribedSymbols.delete(symbol);
    console.log(`📉 Unsubscribed from ${symbol}`);
    
    // Stop updates if no more subscriptions
    if (this.subscribedSymbols.size === 0) {
      this.stopRealTimeUpdates();
    }
  }

  /**
   * Get list of subscribed symbols
   */
  public getSubscribedSymbols(): string[] {
    return Array.from(this.subscribedSymbols);
  }

  /**
   * Search for symbols
   */
  public async searchSymbols(query: string): Promise<any[]> {
    try {
      const results = await yahooFinance.search(query);
      return results.quotes || [];
    } catch (error) {
      console.error(`❌ Error searching symbols for query "${query}":`, error);
      throw new Error(`Failed to search symbols for "${query}"`);
    }
  }
}

// Export singleton instance
export const marketDataService = MarketDataService.getInstance();
export default marketDataService;
