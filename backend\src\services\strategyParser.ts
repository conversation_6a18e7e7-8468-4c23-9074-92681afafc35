import { StrategyParameters, EntryCondition, ExitCondition, RiskManagement } from '../types';

interface ParsedStrategy {
  name: string;
  description: string;
  asset: string;
  parameters: StrategyParameters;
}

class StrategyParserService {
  private static instance: StrategyParserService;

  private constructor() {}

  public static getInstance(): StrategyParserService {
    if (!StrategyParserService.instance) {
      StrategyParserService.instance = new StrategyParserService();
    }
    return StrategyParserService.instance;
  }

  /**
   * Parse natural language strategy description into structured parameters
   */
  public parseStrategy(naturalLanguage: string): ParsedStrategy {
    const text = naturalLanguage.toLowerCase().trim();
    
    // Extract asset/stock name
    const asset = this.extractAsset(text);
    
    // Extract entry conditions
    const entryConditions = this.extractEntryConditions(text);
    
    // Extract exit conditions
    const exitConditions = this.extractExitConditions(text);
    
    // Extract risk management
    const riskManagement = this.extractRiskManagement(text);
    
    // Extract timeframe
    const timeframe = this.extractTimeframe(text);
    
    // Extract position size
    const maxPositionSize = this.extractPositionSize(text);
    
    // Generate strategy name and description
    const name = this.generateStrategyName(entryConditions, asset);
    const description = this.generateDescription(naturalLanguage);

    const parameters: StrategyParameters = {
      entryConditions,
      exitConditions,
      riskManagement,
      timeframe,
      maxPositionSize
    };

    return {
      name,
      description,
      asset,
      parameters
    };
  }

  /**
   * Extract asset/stock name from text
   */
  private extractAsset(text: string): string {
    // Common Indian stock patterns
    const stockPatterns = [
      /\b(reliance|ril)\b/i,
      /\b(tcs|tata consultancy)\b/i,
      /\b(hdfc|hdfcbank)\b/i,
      /\b(infy|infosys)\b/i,
      /\b(icici|icicibank)\b/i,
      /\b(sbi|sbin|state bank)\b/i,
      /\b(bharti|bhartiartl|airtel)\b/i,
      /\b(itc)\b/i,
      /\b(wipro)\b/i,
      /\b(maruti|maruti suzuki)\b/i,
      /\b(bajaj|bajfinance)\b/i,
      /\b(hcl|hcltech)\b/i,
      /\b(tatamotors|tata motors)\b/i,
      /\b(hindunilvr|hindustan unilever)\b/i,
      /\b(kotakbank|kotak)\b/i
    ];

    const stockMap: { [key: string]: string } = {
      'reliance': 'RELIANCE',
      'ril': 'RELIANCE',
      'tcs': 'TCS',
      'tata consultancy': 'TCS',
      'hdfc': 'HDFCBANK',
      'hdfcbank': 'HDFCBANK',
      'infy': 'INFY',
      'infosys': 'INFY',
      'icici': 'ICICIBANK',
      'icicibank': 'ICICIBANK',
      'sbi': 'SBIN',
      'sbin': 'SBIN',
      'state bank': 'SBIN',
      'bharti': 'BHARTIARTL',
      'bhartiartl': 'BHARTIARTL',
      'airtel': 'BHARTIARTL',
      'itc': 'ITC',
      'wipro': 'WIPRO',
      'maruti': 'MARUTI',
      'maruti suzuki': 'MARUTI',
      'bajaj': 'BAJFINANCE',
      'bajfinance': 'BAJFINANCE',
      'hcl': 'HCLTECH',
      'hcltech': 'HCLTECH',
      'tatamotors': 'TATAMOTORS',
      'tata motors': 'TATAMOTORS',
      'hindunilvr': 'HINDUNILVR',
      'hindustan unilever': 'HINDUNILVR',
      'kotakbank': 'KOTAKBANK',
      'kotak': 'KOTAKBANK'
    };

    for (const pattern of stockPatterns) {
      const match = text.match(pattern);
      if (match) {
        const key = match[0].toLowerCase();
        return stockMap[key] || key.toUpperCase();
      }
    }

    // If no specific stock found, look for generic patterns
    const genericMatch = text.match(/\b([A-Z]{2,10})\b/);
    if (genericMatch) {
      return genericMatch[1];
    }

    return 'RELIANCE'; // Default fallback
  }

  /**
   * Extract entry conditions from text
   */
  private extractEntryConditions(text: string): EntryCondition[] {
    const conditions: EntryCondition[] = [];

    // MACD patterns
    if (text.includes('macd') && (text.includes('cross') || text.includes('above'))) {
      conditions.push({
        type: 'MACD_CROSS',
        parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
      });
    }

    // RSI patterns
    if (text.includes('rsi') && (text.includes('below') || text.includes('oversold'))) {
      const levelMatch = text.match(/below\s+(\d+)/);
      const level = levelMatch ? parseInt(levelMatch[1]) : 30;
      conditions.push({
        type: 'RSI_OVERSOLD',
        parameters: { period: 14, oversoldLevel: level }
      });
    }

    // Volume patterns
    if (text.includes('volume') && (text.includes('breakout') || text.includes('spike'))) {
      const multiplierMatch = text.match(/(\d+)x|(\d+)\s*times/);
      const multiplier = multiplierMatch ? parseInt(multiplierMatch[1] || multiplierMatch[2]) : 2;
      conditions.push({
        type: 'VOLUME_BREAKOUT',
        parameters: { multiplier }
      });
    }

    // Price breakout patterns
    if (text.includes('breakout') || text.includes('above') && (text.includes('resistance') || text.includes('moving average') || text.includes('ma'))) {
      conditions.push({
        type: 'PRICE_BREAKOUT',
        parameters: { period: 20 }
      });
    }

    // Default condition if none found
    if (conditions.length === 0) {
      conditions.push({
        type: 'MACD_CROSS',
        parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
      });
    }

    return conditions;
  }

  /**
   * Extract exit conditions from text
   */
  private extractExitConditions(text: string): ExitCondition[] {
    const conditions: ExitCondition[] = [];

    // Stop loss patterns
    const stopLossMatch = text.match(/stop\s*loss\s*(?:at\s*)?(\d+)%?/i) || 
                         text.match(/sl\s*(?:at\s*)?(\d+)%?/i) ||
                         text.match(/(\d+)%\s*stop/i);
    if (stopLossMatch) {
      const percentage = parseInt(stopLossMatch[1]);
      conditions.push({
        type: 'STOP_LOSS',
        parameters: { percentage }
      });
    }

    // Take profit patterns
    const takeProfitMatch = text.match(/take\s*profit\s*(?:at\s*)?(\d+)%?/i) || 
                           text.match(/tp\s*(?:at\s*)?(\d+)%?/i) ||
                           text.match(/(\d+)%\s*profit/i) ||
                           text.match(/target\s*(?:at\s*)?(\d+)%?/i);
    if (takeProfitMatch) {
      const percentage = parseInt(takeProfitMatch[1]);
      conditions.push({
        type: 'TAKE_PROFIT',
        parameters: { percentage }
      });
    }

    // RSI overbought exit
    if (text.includes('rsi') && (text.includes('above') || text.includes('overbought'))) {
      const levelMatch = text.match(/above\s+(\d+)/);
      const level = levelMatch ? parseInt(levelMatch[1]) : 70;
      conditions.push({
        type: 'RSI_OVERBOUGHT',
        parameters: { overboughtLevel: level }
      });
    }

    // Time-based exit
    const timeMatch = text.match(/hold\s*(?:for\s*)?(\d+)\s*days?/i) ||
                     text.match(/(\d+)\s*days?\s*hold/i);
    if (timeMatch) {
      const days = parseInt(timeMatch[1]);
      conditions.push({
        type: 'TIME_BASED',
        parameters: { maxHoldDays: days }
      });
    }

    // Default conditions if none found
    if (conditions.length === 0) {
      conditions.push(
        { type: 'STOP_LOSS', parameters: { percentage: 5 } },
        { type: 'TAKE_PROFIT', parameters: { percentage: 15 } }
      );
    }

    return conditions;
  }

  /**
   * Extract risk management parameters
   */
  private extractRiskManagement(text: string): RiskManagement {
    // Extract stop loss percentage
    const stopLossMatch = text.match(/stop\s*loss\s*(?:at\s*)?(\d+)%?/i);
    const stopLossPercentage = stopLossMatch ? parseInt(stopLossMatch[1]) : 5;

    // Extract take profit percentage
    const takeProfitMatch = text.match(/take\s*profit\s*(?:at\s*)?(\d+)%?/i);
    const takeProfitPercentage = takeProfitMatch ? parseInt(takeProfitMatch[1]) : 15;

    // Extract max drawdown
    const drawdownMatch = text.match(/drawdown\s*(?:of\s*)?(\d+)%?/i);
    const maxDrawdownPercentage = drawdownMatch ? parseInt(drawdownMatch[1]) : 10;

    // Determine position sizing method
    let positionSizing: 'FIXED' | 'PERCENTAGE' | 'KELLY' = 'PERCENTAGE';
    if (text.includes('fixed') || text.includes('₹') || text.includes('rupees')) {
      positionSizing = 'FIXED';
    } else if (text.includes('kelly')) {
      positionSizing = 'KELLY';
    }

    return {
      stopLossPercentage,
      takeProfitPercentage,
      maxDrawdownPercentage,
      positionSizing
    };
  }

  /**
   * Extract timeframe from text
   */
  private extractTimeframe(text: string): string {
    if (text.includes('1 minute') || text.includes('1m')) return '1m';
    if (text.includes('5 minute') || text.includes('5m')) return '5m';
    if (text.includes('15 minute') || text.includes('15m')) return '15m';
    if (text.includes('30 minute') || text.includes('30m')) return '30m';
    if (text.includes('1 hour') || text.includes('1h')) return '1h';
    if (text.includes('4 hour') || text.includes('4h')) return '4h';
    if (text.includes('daily') || text.includes('1d')) return '1d';
    if (text.includes('weekly') || text.includes('1w')) return '1w';
    
    return '1d'; // Default to daily
  }

  /**
   * Extract position size from text
   */
  private extractPositionSize(text: string): number {
    // Look for rupee amounts
    const rupeeMatch = text.match(/₹\s*(\d+(?:,\d+)*)/i) || 
                      text.match(/(\d+(?:,\d+)*)\s*rupees?/i) ||
                      text.match(/rs\.?\s*(\d+(?:,\d+)*)/i);
    
    if (rupeeMatch) {
      return parseInt(rupeeMatch[1].replace(/,/g, ''));
    }

    // Look for percentage
    const percentMatch = text.match(/(\d+)%\s*of\s*portfolio/i);
    if (percentMatch) {
      // Convert percentage to approximate rupee amount (assuming 100k portfolio)
      return (parseInt(percentMatch[1]) / 100) * 100000;
    }

    return 10000; // Default 10k rupees
  }

  /**
   * Generate strategy name based on conditions
   */
  private generateStrategyName(entryConditions: EntryCondition[], asset: string): string {
    const conditionNames = entryConditions.map(condition => {
      switch (condition.type) {
        case 'MACD_CROSS': return 'MACD';
        case 'RSI_OVERSOLD': return 'RSI';
        case 'VOLUME_BREAKOUT': return 'Volume';
        case 'PRICE_BREAKOUT': return 'Breakout';
        default: return 'Custom';
      }
    });

    return `${conditionNames.join(' + ')} Strategy - ${asset}`;
  }

  /**
   * Generate description from natural language
   */
  private generateDescription(naturalLanguage: string): string {
    return `Auto-generated strategy: ${naturalLanguage}`;
  }

  /**
   * Validate parsed strategy
   */
  public validateStrategy(strategy: ParsedStrategy): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!strategy.name || strategy.name.trim().length === 0) {
      errors.push('Strategy name is required');
    }

    if (!strategy.asset || strategy.asset.trim().length === 0) {
      errors.push('Asset/stock symbol is required');
    }

    if (!strategy.parameters.entryConditions || strategy.parameters.entryConditions.length === 0) {
      errors.push('At least one entry condition is required');
    }

    if (!strategy.parameters.exitConditions || strategy.parameters.exitConditions.length === 0) {
      errors.push('At least one exit condition is required');
    }

    if (strategy.parameters.maxPositionSize <= 0) {
      errors.push('Position size must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const strategyParserService = StrategyParserService.getInstance();
export default strategyParserService;
