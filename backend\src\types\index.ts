// Strategy Types
export interface Strategy {
  id: string;
  name: string;
  description: string;
  asset: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  createdAt: Date;
  updatedAt: Date;
  parameters: StrategyParameters;
}

export interface StrategyParameters {
  entryConditions: EntryCondition[];
  exitConditions: ExitCondition[];
  riskManagement: RiskManagement;
  timeframe: string;
  maxPositionSize: number;
}

export interface EntryCondition {
  type: 'MACD_CROSS' | 'RSI_OVERSOLD' | 'RSI_OVERBOUGHT' | 'VOLUME_BREAKOUT' | 'PRICE_BREAKOUT';
  parameters: Record<string, any>;
}

export interface ExitCondition {
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TIME_BASED' | 'INDICATOR_SIGNAL';
  parameters: Record<string, any>;
}

export interface RiskManagement {
  stopLossPercentage: number;
  takeProfitPercentage: number;
  maxDrawdownPercentage: number;
  positionSizing: 'FIXED' | 'PERCENTAGE' | 'KELLY';
}

// Market Data Types
export interface MarketData {
  symbol: string;
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjustedClose?: number;
}

export interface RealTimePrice {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  timestamp: Date;
  volume: number;
}

// Portfolio Types
export interface Portfolio {
  id: string;
  userId: string;
  totalValue: number;
  cash: number;
  holdings: Holding[];
  dailyPnL: number;
  totalPnL: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Holding {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
}

// Trade Types
export interface Trade {
  id: string;
  strategyId: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: Date;
  status: 'PENDING' | 'EXECUTED' | 'CANCELLED' | 'FAILED';
  fees: number;
}

// Backtest Types
export interface BacktestRequest {
  strategyId: string;
  symbol: string;
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  parameters?: StrategyParameters;
}

export interface BacktestResult {
  id: string;
  strategyId: string;
  symbol: string;
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  totalReturnPercent: number;
  maxDrawdown: number;
  maxDrawdownPercent: number;
  sharpeRatio: number;
  winRate: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  trades: BacktestTrade[];
  equity: EquityPoint[];
  createdAt: Date;
}

export interface BacktestTrade {
  timestamp: Date;
  type: 'BUY' | 'SELL';
  price: number;
  quantity: number;
  pnl?: number;
  reason: string;
}

export interface EquityPoint {
  timestamp: Date;
  equity: number;
  drawdown: number;
}

// Technical Indicators
export interface TechnicalIndicators {
  sma?: number[];
  ema?: number[];
  rsi?: number[];
  macd?: {
    macd: number[];
    signal: number[];
    histogram: number[];
  };
  bollinger?: {
    upper: number[];
    middle: number[];
    lower: number[];
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
