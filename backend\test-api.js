const axios = require('axios');

async function testNLPAPI() {
  const baseURL = 'http://localhost:3001';
  
  console.log('🧪 Testing Kuber.ai NLP Strategy API...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test 2: API info
    console.log('2. Testing API info endpoint...');
    const apiResponse = await axios.get(`${baseURL}/api`);
    console.log('✅ API info:', apiResponse.data);
    console.log('');

    // Test 3: Parse natural language strategy
    console.log('3. Testing NLP strategy parsing...');
    const parseResponse = await axios.post(`${baseURL}/api/nlp-strategy/parse`, {
      naturalLanguage: 'Buy RELIANCE when MACD crosses above signal line, stop loss at 5%, take profit at 15%'
    });
    console.log('✅ Parsed strategy:', JSON.stringify(parseResponse.data, null, 2));
    console.log('');

    // Test 4: Get strategy examples
    console.log('4. Testing strategy examples...');
    const examplesResponse = await axios.get(`${baseURL}/api/nlp-strategy/examples`);
    console.log('✅ Strategy examples:', JSON.stringify(examplesResponse.data, null, 2));
    console.log('');

    // Test 5: Quick test
    console.log('5. Testing quick strategy test...');
    const quickTestResponse = await axios.post(`${baseURL}/api/nlp-strategy/quick-test`, {
      naturalLanguage: 'Buy TCS when RSI drops below 30, sell when RSI goes above 70'
    });
    console.log('✅ Quick test result:', JSON.stringify(quickTestResponse.data, null, 2));
    console.log('');

    // Test 6: Create and backtest
    console.log('6. Testing create and backtest...');
    const backtestResponse = await axios.post(`${baseURL}/api/nlp-strategy/create-and-backtest`, {
      naturalLanguage: 'Buy HDFC when volume breakout with 2x volume, stop loss 4%, take profit 12%',
      backtestPeriod: '1yr',
      initialCapital: 100000
    });
    console.log('✅ Backtest result:', JSON.stringify(backtestResponse.data, null, 2));
    console.log('');

    console.log('🎉 All tests passed! The NLP Strategy API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
testNLPAPI();
