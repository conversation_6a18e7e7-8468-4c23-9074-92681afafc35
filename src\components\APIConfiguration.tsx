import React, { useState, useEffect } from 'react';
import { Settings, Globe, Database, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { apiService } from '../services/api';

interface APIConfig {
  indianMarketAPI: {
    baseUrl: string;
    useRealAPI: boolean;
    status: string;
  };
  features: {
    nlpStrategy: boolean;
    backtesting: boolean;
    realTimeData: boolean;
    indianStocks: boolean;
  };
}

interface ConnectionTest {
  success: boolean;
  data: {
    mode: string;
    status?: string;
    message: string;
    instruction?: string;
    apiUrl?: string;
    error?: string;
  };
}

const APIConfiguration: React.FC = () => {
  const [config, setConfig] = useState<APIConfig | null>(null);
  const [connectionTest, setConnectionTest] = useState<ConnectionTest | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await apiService.api.get('/api/config');
      if (response.data.success) {
        setConfig(response.data.data);
      }
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to load configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await apiService.api.get('/api/market-data/test-connection');
      setConnectionTest(response.data);
    } catch (error: any) {
      setConnectionTest({
        success: false,
        data: {
          mode: 'error',
          message: error.response?.data?.error || 'Connection test failed'
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !config) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading configuration...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Settings className="h-8 w-8 text-gray-600" />
          <h1 className="text-3xl font-bold text-gray-900">API Configuration</h1>
        </div>
        <p className="text-gray-600">
          Configure your Indian Market Data API and system settings
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="font-medium">Error</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </div>
      )}

      {/* Indian Market API Configuration */}
      {config && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Globe className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Indian Market Data API</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Current Configuration</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">API URL:</span>
                  <span className="font-mono text-gray-900">{config.indianMarketAPI.baseUrl}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Mode:</span>
                  <span className={`font-medium ${config.indianMarketAPI.useRealAPI ? 'text-green-600' : 'text-orange-600'}`}>
                    {config.indianMarketAPI.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Real API:</span>
                  <span className={`font-medium ${config.indianMarketAPI.useRealAPI ? 'text-green-600' : 'text-gray-600'}`}>
                    {config.indianMarketAPI.useRealAPI ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Connection Test</h3>
              <button
                onClick={testConnection}
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
                <span>Test Connection</span>
              </button>
              
              {connectionTest && (
                <div className={`mt-3 p-3 rounded-md ${connectionTest.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <div className={`flex items-center space-x-2 ${connectionTest.success ? 'text-green-600' : 'text-red-600'}`}>
                    {connectionTest.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                    <span className="font-medium text-sm">
                      {connectionTest.data.mode === 'mock' ? 'Mock Mode' : connectionTest.success ? 'Connected' : 'Failed'}
                    </span>
                  </div>
                  <p className={`text-sm mt-1 ${connectionTest.success ? 'text-green-700' : 'text-red-700'}`}>
                    {connectionTest.data.message}
                  </p>
                  {connectionTest.data.instruction && (
                    <p className="text-sm mt-1 text-blue-600 font-medium">
                      💡 {connectionTest.data.instruction}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Feature Status */}
      {config && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Database className="h-6 w-6 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-900">System Features</h2>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(config.features).map(([feature, enabled]) => (
              <div key={feature} className="text-center p-3 bg-gray-50 rounded-md">
                <div className={`text-2xl mb-1 ${enabled ? 'text-green-600' : 'text-gray-400'}`}>
                  {enabled ? '✅' : '❌'}
                </div>
                <div className="text-sm font-medium text-gray-900 capitalize">
                  {feature.replace(/([A-Z])/g, ' $1').trim()}
                </div>
                <div className={`text-xs ${enabled ? 'text-green-600' : 'text-gray-500'}`}>
                  {enabled ? 'Enabled' : 'Disabled'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-semibold text-blue-900 mb-3">🚀 How to Enable Real Indian Market Data</h3>
        <div className="space-y-2 text-sm text-blue-800">
          <p><strong>Step 1:</strong> Get access to the Indian Market Data API</p>
          <p><strong>Step 2:</strong> Update the <code className="bg-blue-100 px-1 rounded">INDIAN_MARKET_API_URL</code> in your backend <code className="bg-blue-100 px-1 rounded">.env</code> file</p>
          <p><strong>Step 3:</strong> Set <code className="bg-blue-100 px-1 rounded">USE_REAL_INDIAN_API=true</code> in your backend <code className="bg-blue-100 px-1 rounded">.env</code> file</p>
          <p><strong>Step 4:</strong> Restart the backend server</p>
          <p><strong>Step 5:</strong> Test the connection using the button above</p>
        </div>
        
        <div className="mt-4 p-3 bg-blue-100 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>API Endpoint Format:</strong> <code>GET /historical_data?stock_name=TATAMOTORS&period=1yr&filter=price</code>
          </p>
          <p className="text-sm text-blue-800 mt-1">
            <strong>Expected Response:</strong> <code>{"{"}"datasets": [{"{"}"metric": "Price", "values": [["2024-06-27", "3934.15"], ...]{"}"}, ...]{"}"}</code>
          </p>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="text-center">
        <button
          onClick={loadConfiguration}
          disabled={isLoading}
          className="flex items-center space-x-2 mx-auto px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
          <span>Refresh Configuration</span>
        </button>
      </div>
    </div>
  );
};

export default APIConfiguration;
