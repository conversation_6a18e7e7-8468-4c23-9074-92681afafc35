import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Activity, Bot, AlertTriangle } from 'lucide-react';
import MetricCard from './MetricCard';
import StrategyCard from './StrategyCard';
import RecentActivity from './RecentActivity';
import { useTradingContext } from '../contexts/TradingContext';

const Dashboard: React.FC = () => {
  const { strategies, portfolioValue, dailyPnL, isLoading, error } = useTradingContext();

  // Calculate metrics from real data
  const activeStrategiesCount = strategies.filter(s => s.status === 'active').length;
  const totalPnL = strategies.reduce((sum, s) => sum + s.pnl, 0);
  const winRate = strategies.length > 0 ?
    (strategies.filter(s => s.pnl > 0).length / strategies.length * 100).toFixed(0) : '0';

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const metrics = [
    {
      title: 'Total Portfolio Value',
      value: portfolioValue > 0 ? formatCurrency(portfolioValue) : 'Loading...',
      change: dailyPnL > 0 ? `+${((dailyPnL / portfolioValue) * 100).toFixed(1)}%` : 'N/A',
      positive: dailyPnL >= 0,
      icon: DollarSign,
    },
    {
      title: 'Active Strategies',
      value: activeStrategiesCount.toString(),
      change: `${strategies.length} total`,
      positive: true,
      icon: Bot,
    },
    {
      title: 'Today\'s P&L',
      value: dailyPnL !== 0 ? formatCurrency(dailyPnL) : 'No data',
      change: dailyPnL > 0 ? `+${((dailyPnL / portfolioValue) * 100).toFixed(1)}%` : 'N/A',
      positive: dailyPnL >= 0,
      icon: dailyPnL >= 0 ? TrendingUp : TrendingDown,
    },
    {
      title: 'Win Rate',
      value: `${winRate}%`,
      change: `${strategies.filter(s => s.pnl > 0).length} profitable`,
      positive: parseInt(winRate) >= 50,
      icon: Activity,
    },
  ];

  // Use real strategies data
  const activeStrategies = strategies.slice(0, 3).map(strategy => ({
    name: strategy.name,
    asset: strategy.asset,
    status: strategy.status,
    pnl: strategy.pnl >= 0 ? `+${formatCurrency(strategy.pnl)}` : formatCurrency(strategy.pnl),
    description: strategy.description,
  }));

  return (
    <div className="space-y-8 py-8">
      {/* Header */}
      <div className="text-center md:text-left">
        <h1 className="text-3xl md:text-4xl font-bold mb-2">
          Welcome back! 👋
        </h1>
        <p className="text-gray-400 text-lg">
          {isLoading ? 'Loading your portfolio...' :
           error ? 'Unable to load portfolio data' :
           `Managing ${strategies.length} strategies with ${formatCurrency(portfolioValue)} portfolio value`}
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Active Strategies */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold flex items-center space-x-2">
                <Bot className="w-5 h-5 text-blue-400" />
                <span>Active Strategies</span>
              </h2>
              <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {isLoading ? (
                <div className="text-center py-8 text-gray-400">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  Loading strategies...
                </div>
              ) : activeStrategies.length > 0 ? (
                activeStrategies.map((strategy, index) => (
                  <StrategyCard key={index} {...strategy} />
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No strategies yet. Create your first AI strategy!</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Market Status */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Activity className="w-5 h-5 text-green-400" />
              <span>Market Status</span>
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">NSE</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Open</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">BSE</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Open</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Crypto</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">24/7</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <RecentActivity />

          {/* Quick Actions */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-500 hover:bg-blue-600 rounded-lg py-3 px-4 font-medium transition-colors">
                Create New Strategy
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded-lg py-3 px-4 font-medium transition-colors">
                Backtest Strategy
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded-lg py-3 px-4 font-medium transition-colors">
                View Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;