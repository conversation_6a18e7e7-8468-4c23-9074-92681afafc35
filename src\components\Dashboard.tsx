import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Activity, Bot, AlertTriangle } from 'lucide-react';
import MetricCard from './MetricCard';
import StrategyCard from './StrategyCard';
import RecentActivity from './RecentActivity';

const Dashboard: React.FC = () => {
  const metrics = [
    {
      title: 'Total Portfolio Value',
      value: '₹2,45,680',
      change: '+12.5%',
      positive: true,
      icon: DollarSign,
    },
    {
      title: 'Active Strategies',
      value: '8',
      change: '+2 this week',
      positive: true,
      icon: Bot,
    },
    {
      title: 'Today\'s P&L',
      value: '₹3,240',
      change: '+2.8%',
      positive: true,
      icon: TrendingUp,
    },
    {
      title: 'Win Rate',
      value: '78%',
      change: '+5% this month',
      positive: true,
      icon: Activity,
    },
  ];

  const activeStrategies = [
    {
      name: 'MACD Momentum',
      asset: 'RELIANCE',
      status: 'active',
      pnl: '+₹1,250',
      description: 'Buy when MACD crosses above signal line',
    },
    {
      name: 'RSI Oversold',
      asset: 'TATAMOTORS',
      status: 'active',
      pnl: '+₹850',
      description: 'Buy when RSI drops below 30',
    },
    {
      name: 'Breakout Strategy',
      asset: 'INFY',
      status: 'paused',
      pnl: '-₹120',
      description: 'Buy on volume breakout above resistance',
    },
  ];

  return (
    <div className="space-y-8 py-8">
      {/* Header */}
      <div className="text-center md:text-left">
        <h1 className="text-3xl md:text-4xl font-bold mb-2">
          Welcome back, Trader! 👋
        </h1>
        <p className="text-gray-400 text-lg">
          Your automated trading strategies are working hard for you.
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Active Strategies */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold flex items-center space-x-2">
                <Bot className="w-5 h-5 text-blue-400" />
                <span>Active Strategies</span>
              </h2>
              <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {activeStrategies.map((strategy, index) => (
                <StrategyCard key={index} {...strategy} />
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Market Status */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Activity className="w-5 h-5 text-green-400" />
              <span>Market Status</span>
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">NSE</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Open</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">BSE</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Open</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Crypto</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">24/7</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <RecentActivity />

          {/* Quick Actions */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-500 hover:bg-blue-600 rounded-lg py-3 px-4 font-medium transition-colors">
                Create New Strategy
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded-lg py-3 px-4 font-medium transition-colors">
                Backtest Strategy
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded-lg py-3 px-4 font-medium transition-colors">
                View Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;