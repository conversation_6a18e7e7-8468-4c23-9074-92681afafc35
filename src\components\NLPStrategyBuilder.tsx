import React, { useState, useEffect } from 'react';
import { Brain, Zap, TrendingUp, AlertCircle, CheckCircle, Loader2, Play, Code } from 'lucide-react';
import { apiService } from '../services/api';

interface StrategyExample {
  category: string;
  examples: string[];
}

const NLPStrategyBuilder: React.FC = () => {
  const [naturalLanguage, setNaturalLanguage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [parsedStrategy, setParsedStrategy] = useState<any>(null);
  const [backtestResult, setBacktestResult] = useState<any>(null);
  const [examples, setExamples] = useState<StrategyExample[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'create' | 'examples' | 'results'>('create');

  useEffect(() => {
    loadExamples();
  }, []);

  const loadExamples = async () => {
    try {
      const response = await apiService.getStrategyExamples();
      if (response.success) {
        setExamples(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load examples:', error);
    }
  };

  const handleParseStrategy = async () => {
    if (!naturalLanguage.trim()) {
      setError('Please enter a strategy description');
      return;
    }

    setIsLoading(true);
    setError(null);
    setParsedStrategy(null);
    setBacktestResult(null);

    try {
      const response = await apiService.parseNaturalLanguage(naturalLanguage);
      if (response.success) {
        setParsedStrategy(response.data);
        setActiveTab('results');
      } else {
        setError(response.error || 'Failed to parse strategy');
      }
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to parse strategy');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAndBacktest = async () => {
    if (!naturalLanguage.trim()) {
      setError('Please enter a strategy description');
      return;
    }

    setIsLoading(true);
    setError(null);
    setParsedStrategy(null);
    setBacktestResult(null);

    try {
      const response = await apiService.createAndBacktestStrategy(naturalLanguage, '1yr', 100000);
      if (response.success) {
        setParsedStrategy(response.data);
        setBacktestResult(response.data?.backtestResult);
        setActiveTab('results');
      } else {
        setError(response.error || 'Failed to create and backtest strategy');
      }
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to create and backtest strategy');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExampleClick = (example: string) => {
    setNaturalLanguage(example);
    setActiveTab('create');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Brain className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">AI Strategy Builder</h1>
        </div>
        <p className="text-gray-600">
          Write your trading strategy in plain English and let AI convert it to executable code
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Create Strategy
        </button>
        <button
          onClick={() => setActiveTab('examples')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'examples'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Examples
        </button>
        <button
          onClick={() => setActiveTab('results')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'results'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Results
        </button>
      </div>

      {/* Create Strategy Tab */}
      {activeTab === 'create' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Describe your trading strategy in plain English:
            </label>
            <textarea
              value={naturalLanguage}
              onChange={(e) => setNaturalLanguage(e.target.value)}
              placeholder="Example: Buy RELIANCE when MACD crosses above signal line, stop loss at 5%, take profit at 15%"
              className="w-full h-32 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            
            {error && (
              <div className="mt-3 flex items-center space-x-2 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            <div className="mt-4 flex space-x-3">
              <button
                onClick={handleParseStrategy}
                disabled={isLoading || !naturalLanguage.trim()}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Brain className="h-4 w-4" />}
                <span>Parse Strategy</span>
              </button>
              
              <button
                onClick={handleCreateAndBacktest}
                disabled={isLoading || !naturalLanguage.trim()}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                <span>Create & Backtest</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Examples Tab */}
      {activeTab === 'examples' && (
        <div className="space-y-6">
          {examples.map((category, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{category.category}</h3>
              <div className="space-y-2">
                {category.examples.map((example, exampleIndex) => (
                  <button
                    key={exampleIndex}
                    onClick={() => handleExampleClick(example)}
                    className="w-full text-left p-3 bg-gray-50 hover:bg-blue-50 rounded-md transition-colors border border-transparent hover:border-blue-200"
                  >
                    <span className="text-sm text-gray-700">{example}</span>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Results Tab */}
      {activeTab === 'results' && (
        <div className="space-y-6">
          {parsedStrategy && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold text-gray-900">Parsed Strategy</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Strategy Details</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Name:</span> {parsedStrategy.strategy?.name}</div>
                    <div><span className="font-medium">Asset:</span> {parsedStrategy.strategy?.asset}</div>
                    <div><span className="font-medium">Timeframe:</span> {parsedStrategy.strategy?.parameters?.timeframe}</div>
                    <div><span className="font-medium">Max Position:</span> {formatCurrency(parsedStrategy.strategy?.parameters?.maxPositionSize)}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Risk Management</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Stop Loss:</span> {parsedStrategy.strategy?.parameters?.riskManagement?.stopLossPercentage}%</div>
                    <div><span className="font-medium">Take Profit:</span> {parsedStrategy.strategy?.parameters?.riskManagement?.takeProfitPercentage}%</div>
                    <div><span className="font-medium">Position Sizing:</span> {parsedStrategy.strategy?.parameters?.riskManagement?.positionSizing}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {backtestResult && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Backtest Results</h3>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-green-600">
                    {backtestResult.totalReturnPercent?.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">Total Return</div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-blue-600">
                    {backtestResult.winRate?.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">Win Rate</div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-purple-600">
                    {backtestResult.sharpeRatio?.toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-600">Sharpe Ratio</div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-orange-600">
                    {backtestResult.totalTrades}
                  </div>
                  <div className="text-sm text-gray-600">Total Trades</div>
                </div>
              </div>
              
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="flex justify-between">
                    <span>Initial Capital:</span>
                    <span className="font-medium">{formatCurrency(backtestResult.initialCapital)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Final Capital:</span>
                    <span className="font-medium">{formatCurrency(backtestResult.finalCapital)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Return:</span>
                    <span className="font-medium text-green-600">{formatCurrency(backtestResult.totalReturn)}</span>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between">
                    <span>Max Drawdown:</span>
                    <span className="font-medium text-red-600">{backtestResult.maxDrawdownPercent?.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Profit Factor:</span>
                    <span className="font-medium">{backtestResult.profitFactor?.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Win/Loss:</span>
                    <span className="font-medium">{formatCurrency(backtestResult.averageWin)} / {formatCurrency(backtestResult.averageLoss)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!parsedStrategy && !backtestResult && (
            <div className="text-center py-12 text-gray-500">
              <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No results yet. Create a strategy to see results here.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NLPStrategyBuilder;
