import React from 'react';
import { PieChart, TrendingUp, TrendingDown, DollarSign, Target } from 'lucide-react';

const Portfolio: React.FC = () => {
  const holdings = [
    {
      symbol: 'RELIANCE',
      name: 'Reliance Industries',
      quantity: 50,
      avgPrice: 2450,
      currentPrice: 2580,
      value: 129000,
      pnl: 6500,
      pnlPercentage: 5.3,
    },
    {
      symbol: 'TATAMOTORS',
      name: 'Tata Motors',
      quantity: 100,
      avgPrice: 485,
      currentPrice: 465,
      value: 46500,
      pnl: -2000,
      pnlPercentage: -4.1,
    },
    {
      symbol: 'INFY',
      name: 'Infosys',
      quantity: 75,
      avgPrice: 1620,
      currentPrice: 1720,
      value: 129000,
      pnl: 7500,
      pnlPercentage: 6.2,
    },
  ];

  const totalValue = holdings.reduce((sum, holding) => sum + holding.value, 0);
  const totalPnL = holdings.reduce((sum, holding) => sum + holding.pnl, 0);
  const totalPnLPercentage = (totalPnL / (totalValue - totalPnL)) * 100;

  return (
    <div className="space-y-8 py-8">
      {/* Header */}
      <div className="text-center md:text-left">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center md:justify-start space-x-3">
          <PieChart className="w-8 h-8 text-blue-400" />
          <span>Portfolio</span>
        </h1>
        <p className="text-gray-400 text-lg">
          Track your investments and automated trading performance.
        </p>
      </div>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-blue-400" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-400">Total Value</h3>
            </div>
          </div>
          <p className="text-2xl font-bold text-white">₹{totalValue.toLocaleString()}</p>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              totalPnL >= 0 ? 'bg-green-500/20' : 'bg-red-500/20'
            }`}>
              {totalPnL >= 0 ? (
                <TrendingUp className="w-5 h-5 text-green-400" />
              ) : (
                <TrendingDown className="w-5 h-5 text-red-400" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-400">Total P&L</h3>
            </div>
          </div>
          <p className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalPnL >= 0 ? '+' : ''}₹{totalPnL.toLocaleString()}
          </p>
          <p className={`text-sm ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalPnLPercentage >= 0 ? '+' : ''}{totalPnLPercentage.toFixed(2)}%
          </p>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Target className="w-5 h-5 text-purple-400" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-400">Holdings</h3>
            </div>
          </div>
          <p className="text-2xl font-bold text-white">{holdings.length}</p>
        </div>
      </div>

      {/* Holdings Table */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 overflow-hidden">
        <div className="p-6 border-b border-gray-700/50">
          <h2 className="text-xl font-semibold">Holdings</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-900/50">
              <tr className="text-left">
                <th className="px-6 py-4 text-sm font-medium text-gray-400">Symbol</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">Quantity</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">Avg Price</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">Current Price</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">Value</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">P&L</th>
                <th className="px-6 py-4 text-sm font-medium text-gray-400">%</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50">
              {holdings.map((holding, index) => (
                <tr key={index} className="hover:bg-gray-900/30 transition-colors">
                  <td className="px-6 py-4">
                    <div>
                      <div className="font-medium text-white">{holding.symbol}</div>
                      <div className="text-sm text-gray-400">{holding.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-white">{holding.quantity}</td>
                  <td className="px-6 py-4 text-white">₹{holding.avgPrice}</td>
                  <td className="px-6 py-4 text-white">₹{holding.currentPrice}</td>
                  <td className="px-6 py-4 text-white">₹{holding.value.toLocaleString()}</td>
                  <td className={`px-6 py-4 font-medium ${
                    holding.pnl >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {holding.pnl >= 0 ? '+' : ''}₹{holding.pnl.toLocaleString()}
                  </td>
                  <td className={`px-6 py-4 font-medium ${
                    holding.pnlPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {holding.pnlPercentage >= 0 ? '+' : ''}{holding.pnlPercentage.toFixed(2)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Chart Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold mb-4">Portfolio Allocation</h3>
          <div className="space-y-4">
            {holdings.map((holding, index) => {
              const percentage = (holding.value / totalValue) * 100;
              return (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-300">{holding.symbol}</span>
                    <span className="text-sm text-gray-400">{percentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total Return</span>
              <span className={`font-semibold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {totalPnLPercentage >= 0 ? '+' : ''}{totalPnLPercentage.toFixed(2)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Best Performer</span>
              <span className="text-green-400 font-semibold">INFY (+6.2%)</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Worst Performer</span>
              <span className="text-red-400 font-semibold">TATAMOTORS (-4.1%)</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Diversification Score</span>
              <span className="text-blue-400 font-semibold">Good</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Portfolio;