import React from 'react';
import { TrendingUp, TrendingDown, Clock } from 'lucide-react';

const RecentActivity: React.FC = () => {
  const activities = [
    {
      type: 'buy',
      asset: 'RELIANCE',
      amount: '₹10,000',
      time: '2 min ago',
      strategy: 'MACD Momentum',
    },
    {
      type: 'sell',
      asset: 'TATAMOTORS',
      amount: '₹8,500',
      time: '15 min ago',
      strategy: 'RSI Oversold',
    },
    {
      type: 'buy',
      asset: 'INFY',
      amount: '₹12,000',
      time: '1 hr ago',
      strategy: 'Breakout Strategy',
    },
  ];

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
        <Clock className="w-5 h-5 text-blue-400" />
        <span>Recent Activity</span>
      </h3>
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                activity.type === 'buy' ? 'bg-green-400/20' : 'bg-red-400/20'
              }`}>
                {activity.type === 'buy' ? (
                  <TrendingUp className="w-4 h-4 text-green-400" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-400" />
                )}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-white">
                    {activity.type.toUpperCase()} {activity.asset}
                  </span>
                </div>
                <p className="text-xs text-gray-400">{activity.strategy}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-medium text-white">{activity.amount}</p>
              <p className="text-xs text-gray-400">{activity.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentActivity;