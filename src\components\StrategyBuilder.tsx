import React, { useState } from 'react';
import { Bo<PERSON>, Wand2, CheckCircle, AlertCircle, Code, Play } from 'lucide-react';

const StrategyBuilder: React.FC = () => {
  const [naturalLanguage, setNaturalLanguage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [generatedCode, setGeneratedCode] = useState('');
  const [validation, setValidation] = useState<{ isValid: boolean; message: string } | null>(null);

  const examples = [
    "Buy 1000 INR of Reliance when MACD crosses above signal line",
    "Sell 50% of TATAMOTORS position when RSI goes above 70",
    "Buy INFY when volume is 2x average and price breaks above 20-day MA",
    "Set stop loss at 5% below entry price for all positions",
  ];

  const handleProcessStrategy = async () => {
    if (!naturalLanguage.trim()) return;
    
    setIsProcessing(true);
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock generated code
    const mockCode = `
// Generated Trading Strategy
const strategy = {
  name: "Custom Strategy",
  entry: {
    condition: "MACD_CROSS_ABOVE_SIGNAL",
    asset: "RELIANCE",
    quantity: 1000,
    currency: "INR"
  },
  exit: {
    stopLoss: 0.05,
    takeProfit: 0.15
  },
  timeframe: "5m"
};

function checkEntry(marketData) {
  const macd = calculateMACD(marketData);
  return macd.histogram > 0 && macd.prevHistogram <= 0;
}
    `.trim();
    
    setGeneratedCode(mockCode);
    setValidation({
      isValid: true,
      message: "Strategy validation passed! Ready for deployment."
    });
    setIsProcessing(false);
  };

  return (
    <div className="space-y-8 py-8">
      {/* Header */}
      <div className="text-center md:text-left">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center md:justify-start space-x-3">
          <Bot className="w-8 h-8 text-blue-400" />
          <span>Strategy Builder</span>
        </h1>
        <p className="text-gray-400 text-lg">
          Describe your trading strategy in plain English, and our AI will convert it into executable code.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center space-x-2">
              <Wand2 className="w-5 h-5 text-purple-400" />
              <span>Describe Your Strategy</span>
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Natural Language Input
                </label>
                <textarea
                  value={naturalLanguage}
                  onChange={(e) => setNaturalLanguage(e.target.value)}
                  placeholder="Example: Buy 1000 INR of Reliance when MACD crosses above signal line..."
                  className="w-full h-32 bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 resize-none"
                />
              </div>
              
              <button
                onClick={handleProcessStrategy}
                disabled={!naturalLanguage.trim() || isProcessing}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed rounded-lg py-3 px-4 font-medium transition-all duration-200 flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Bot className="w-4 h-4" />
                    <span>Generate Strategy</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Examples */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4">Example Strategies</h3>
            <div className="space-y-3">
              {examples.map((example, index) => (
                <button
                  key={index}
                  onClick={() => setNaturalLanguage(example)}
                  className="w-full text-left p-3 bg-gray-900/50 hover:bg-gray-900 rounded-lg border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200"
                >
                  <p className="text-sm text-gray-300">{example}</p>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Output Section */}
        <div className="space-y-6">
          {/* Generated Code */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center space-x-2">
              <Code className="w-5 h-5 text-green-400" />
              <span>Generated Code</span>
            </h2>
            
            {generatedCode ? (
              <div className="space-y-4">
                <div className="bg-gray-900/80 rounded-lg p-4 border border-gray-700/50">
                  <pre className="text-sm text-gray-300 overflow-x-auto">
                    <code>{generatedCode}</code>
                  </pre>
                </div>
                
                {validation && (
                  <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                    validation.isValid 
                      ? 'bg-green-500/20 border border-green-500/30 text-green-400'
                      : 'bg-red-500/20 border border-red-500/30 text-red-400'
                  }`}>
                    {validation.isValid ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <AlertCircle className="w-5 h-5" />
                    )}
                    <span className="text-sm font-medium">{validation.message}</span>
                  </div>
                )}
                
                <div className="flex space-x-3">
                  <button className="flex-1 bg-blue-500 hover:bg-blue-600 rounded-lg py-3 px-4 font-medium transition-colors flex items-center justify-center space-x-2">
                    <Play className="w-4 h-4" />
                    <span>Deploy Strategy</span>
                  </button>
                  <button className="flex-1 bg-gray-700 hover:bg-gray-600 rounded-lg py-3 px-4 font-medium transition-colors">
                    Backtest First
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-gray-900/50 rounded-lg p-8 border border-gray-700/30 text-center">
                <Code className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">
                  Your generated trading strategy code will appear here.
                </p>
              </div>
            )}
          </div>

          {/* Strategy Configuration */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
            <h3 className="text-lg font-semibold mb-4">Strategy Configuration</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Risk per Trade
                </label>
                <select className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                  <option>1%</option>
                  <option>2%</option>
                  <option>5%</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Positions
                </label>
                <select className="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                  <option>3</option>
                  <option>5</option>
                  <option>10</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyBuilder;