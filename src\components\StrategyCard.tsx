import React from 'react';
import { Play, Pause, MoreVertical } from 'lucide-react';

interface StrategyCardProps {
  name: string;
  asset: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: string;
  description: string;
}

const StrategyCard: React.FC<StrategyCardProps> = ({ name, asset, status, pnl, description }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-400 bg-green-400/20';
      case 'paused':
        return 'text-yellow-400 bg-yellow-400/20';
      case 'stopped':
        return 'text-red-400 bg-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/20';
    }
  };

  const isProfitable = pnl.startsWith('+');

  return (
    <div className="bg-gray-900/50 rounded-xl border border-gray-700/30 p-4 hover:border-gray-600/50 transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-white">{name}</h4>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                {status}
              </span>
            </div>
            <p className="text-sm text-gray-400 mt-1">{asset}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`font-semibold ${isProfitable ? 'text-green-400' : 'text-red-400'}`}>
            {pnl}
          </span>
          <button className="p-1 hover:bg-gray-800 rounded-lg transition-colors">
            <MoreVertical className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>
      <p className="text-sm text-gray-400 mb-4">{description}</p>
      <div className="flex items-center space-x-2">
        <button className="flex items-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium transition-colors">
          {status === 'active' ? (
            <>
              <Pause className="w-3 h-3" />
              <span>Pause</span>
            </>
          ) : (
            <>
              <Play className="w-3 h-3" />
              <span>Resume</span>
            </>
          )}
        </button>
        <button className="text-gray-400 hover:text-white text-sm font-medium transition-colors">
          View Details
        </button>
      </div>
    </div>
  );
};

export default StrategyCard;