import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService, Strategy as ApiStrategy, Portfolio } from '../services/api';

interface Strategy {
  id: string;
  name: string;
  description: string;
  asset: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  createdAt: Date;
}

interface TradingContextType {
  strategies: Strategy[];
  addStrategy: (strategy: Omit<Strategy, 'id' | 'createdAt'>) => void;
  updateStrategy: (id: string, updates: Partial<Strategy>) => void;
  removeStrategy: (id: string) => void;
  portfolioValue: number;
  dailyPnL: number;
  portfolio: Portfolio | null;
  isLoading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

export const useTradingContext = () => {
  const context = useContext(TradingContext);
  if (!context) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};

interface TradingProviderProps {
  children: ReactNode;
}

export const TradingProvider: React.FC<TradingProviderProps> = ({ children }) => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [portfolioValue, setPortfolioValue] = useState(0);
  const [dailyPnL, setDailyPnL] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Convert API strategy to local strategy format
  const convertApiStrategy = (apiStrategy: ApiStrategy): Strategy => ({
    id: apiStrategy.id,
    name: apiStrategy.name,
    description: apiStrategy.description,
    asset: apiStrategy.asset,
    status: apiStrategy.status.toLowerCase() as 'active' | 'paused' | 'stopped',
    pnl: apiStrategy.pnl,
    createdAt: new Date(apiStrategy.createdAt),
  });

  // Load data from API
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load strategies
      const strategiesResponse = await apiService.getStrategies();
      if (strategiesResponse.success && strategiesResponse.data) {
        const convertedStrategies = strategiesResponse.data.map(convertApiStrategy);
        setStrategies(convertedStrategies);
      }

      // Load portfolio
      const portfolioResponse = await apiService.getPortfolio();
      if (portfolioResponse.success && portfolioResponse.data) {
        setPortfolio(portfolioResponse.data);
        setPortfolioValue(portfolioResponse.data.totalValue);
        setDailyPnL(portfolioResponse.data.dailyPnL);
      }
    } catch (error: any) {
      console.error('Failed to load data:', error);
      setError(error.message || 'Failed to load data');

      // Fallback to mock data if API fails
      setStrategies([
        {
          id: '1',
          name: 'MACD Momentum',
          description: 'Buy when MACD crosses above signal line',
          asset: 'RELIANCE',
          status: 'active',
          pnl: 1250,
          createdAt: new Date('2024-01-15'),
        },
        {
          id: '2',
          name: 'RSI Oversold',
          description: 'Buy when RSI drops below 30',
          asset: 'TATAMOTORS',
          status: 'active',
          pnl: 850,
          createdAt: new Date('2024-01-10'),
        },
      ]);
      setPortfolioValue(245680);
      setDailyPnL(3240);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const addStrategy = async (strategy: Omit<Strategy, 'id' | 'createdAt'>) => {
    try {
      const apiStrategy = {
        name: strategy.name,
        description: strategy.description,
        asset: strategy.asset,
        status: strategy.status.toUpperCase() as 'ACTIVE' | 'PAUSED' | 'STOPPED',
        pnl: strategy.pnl,
        parameters: {} // Default empty parameters
      };

      const response = await apiService.createStrategy(apiStrategy);
      if (response.success && response.data) {
        const newStrategy = convertApiStrategy(response.data);
        setStrategies(prev => [...prev, newStrategy]);
      }
    } catch (error) {
      console.error('Failed to add strategy:', error);
      // Fallback to local add
      const newStrategy: Strategy = {
        ...strategy,
        id: Date.now().toString(),
        createdAt: new Date(),
      };
      setStrategies(prev => [...prev, newStrategy]);
    }
  };

  const updateStrategy = async (id: string, updates: Partial<Strategy>) => {
    try {
      const apiUpdates = {
        ...updates,
        status: updates.status?.toUpperCase() as 'ACTIVE' | 'PAUSED' | 'STOPPED' | undefined
      };

      const response = await apiService.updateStrategy(id, apiUpdates);
      if (response.success && response.data) {
        const updatedStrategy = convertApiStrategy(response.data);
        setStrategies(prev =>
          prev.map(strategy =>
            strategy.id === id ? updatedStrategy : strategy
          )
        );
      }
    } catch (error) {
      console.error('Failed to update strategy:', error);
      // Fallback to local update
      setStrategies(prev =>
        prev.map(strategy =>
          strategy.id === id ? { ...strategy, ...updates } : strategy
        )
      );
    }
  };

  const removeStrategy = async (id: string) => {
    try {
      await apiService.deleteStrategy(id);
      setStrategies(prev => prev.filter(strategy => strategy.id !== id));
    } catch (error) {
      console.error('Failed to remove strategy:', error);
      // Fallback to local remove
      setStrategies(prev => prev.filter(strategy => strategy.id !== id));
    }
  };

  const refreshData = async () => {
    await loadData();
  };

  const value: TradingContextType = {
    strategies,
    addStrategy,
    updateStrategy,
    removeStrategy,
    portfolioValue,
    dailyPnL,
    portfolio,
    isLoading,
    error,
    refreshData,
  };

  return (
    <TradingContext.Provider value={value}>
      {children}
    </TradingContext.Provider>
  );
};