import React, { createContext, useContext, useState, ReactNode } from 'react';

interface Strategy {
  id: string;
  name: string;
  description: string;
  asset: string;
  status: 'active' | 'paused' | 'stopped';
  pnl: number;
  createdAt: Date;
}

interface TradingContextType {
  strategies: Strategy[];
  addStrategy: (strategy: Omit<Strategy, 'id' | 'createdAt'>) => void;
  updateStrategy: (id: string, updates: Partial<Strategy>) => void;
  removeStrategy: (id: string) => void;
  portfolioValue: number;
  dailyPnL: number;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

export const useTradingContext = () => {
  const context = useContext(TradingContext);
  if (!context) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};

interface TradingProviderProps {
  children: ReactNode;
}

export const TradingProvider: React.FC<TradingProviderProps> = ({ children }) => {
  const [strategies, setStrategies] = useState<Strategy[]>([
    {
      id: '1',
      name: 'MACD Momentum',
      description: 'Buy when MACD crosses above signal line',
      asset: 'RELIANCE',
      status: 'active',
      pnl: 1250,
      createdAt: new Date('2024-01-15'),
    },
    {
      id: '2',
      name: 'RSI Oversold',
      description: 'Buy when RSI drops below 30',
      asset: 'TATAMOTORS',
      status: 'active',
      pnl: 850,
      createdAt: new Date('2024-01-10'),
    },
    {
      id: '3',
      name: 'Breakout Strategy',
      description: 'Buy on volume breakout above resistance',
      asset: 'INFY',
      status: 'paused',
      pnl: -120,
      createdAt: new Date('2024-01-20'),
    },
  ]);

  const [portfolioValue] = useState(245680);
  const [dailyPnL] = useState(3240);

  const addStrategy = (strategy: Omit<Strategy, 'id' | 'createdAt'>) => {
    const newStrategy: Strategy = {
      ...strategy,
      id: Date.now().toString(),
      createdAt: new Date(),
    };
    setStrategies(prev => [...prev, newStrategy]);
  };

  const updateStrategy = (id: string, updates: Partial<Strategy>) => {
    setStrategies(prev =>
      prev.map(strategy =>
        strategy.id === id ? { ...strategy, ...updates } : strategy
      )
    );
  };

  const removeStrategy = (id: string) => {
    setStrategies(prev => prev.filter(strategy => strategy.id !== id));
  };

  const value: TradingContextType = {
    strategies,
    addStrategy,
    updateStrategy,
    removeStrategy,
    portfolioValue,
    dailyPnL,
  };

  return (
    <TradingContext.Provider value={value}>
      {children}
    </TradingContext.Provider>
  );
};