import axios from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface Strategy {
  id: string;
  name: string;
  description: string;
  asset: string;
  status: 'ACTIVE' | 'PAUSED' | 'STOPPED';
  pnl: number;
  createdAt: string;
  updatedAt: string;
  parameters: any;
}

export interface ParsedStrategy {
  name: string;
  description: string;
  asset: string;
  parameters: {
    entryConditions: any[];
    exitConditions: any[];
    riskManagement: any;
    timeframe: string;
    maxPositionSize: number;
  };
}

export interface BacktestResult {
  id: string;
  strategyId: string;
  symbol: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  totalReturn: number;
  totalReturnPercent: number;
  maxDrawdown: number;
  maxDrawdownPercent: number;
  sharpeRatio: number;
  winRate: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  trades: any[];
  equity: any[];
  createdAt: string;
}

export interface Portfolio {
  id: string;
  name: string;
  totalValue: number;
  cash: number;
  dailyPnL: number;
  totalPnL: number;
  holdings: Holding[];
}

export interface Holding {
  id: string;
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
}

// API Service Class
class ApiService {
  // Health Check
  async healthCheck(): Promise<any> {
    const response = await api.get('/health');
    return response.data;
  }

  // NLP Strategy APIs
  async parseNaturalLanguage(naturalLanguage: string): Promise<ApiResponse<{ strategy: ParsedStrategy; validation: any }>> {
    const response = await api.post('/api/nlp-strategy/parse', { naturalLanguage });
    return response.data;
  }

  async quickTestStrategy(naturalLanguage: string): Promise<ApiResponse<any>> {
    const response = await api.post('/api/nlp-strategy/quick-test', { naturalLanguage });
    return response.data;
  }

  async createAndBacktestStrategy(
    naturalLanguage: string,
    backtestPeriod: string = '1yr',
    initialCapital: number = 100000
  ): Promise<ApiResponse<{ strategy: Strategy; backtestResult: BacktestResult; validation: any }>> {
    const response = await api.post('/api/nlp-strategy/create-and-backtest', {
      naturalLanguage,
      backtestPeriod,
      initialCapital
    });
    return response.data;
  }

  async getStrategyExamples(): Promise<ApiResponse<any[]>> {
    const response = await api.get('/api/nlp-strategy/examples');
    return response.data;
  }

  async deployStrategy(strategyId: string): Promise<ApiResponse<Strategy>> {
    const response = await api.post('/api/nlp-strategy/deploy', { strategyId });
    return response.data;
  }

  // Strategy Management APIs
  async getStrategies(): Promise<ApiResponse<Strategy[]>> {
    const response = await api.get('/api/strategies');
    return response.data;
  }

  async getStrategy(id: string): Promise<ApiResponse<Strategy>> {
    const response = await api.get(`/api/strategies/${id}`);
    return response.data;
  }

  async createStrategy(strategy: Omit<Strategy, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Strategy>> {
    const response = await api.post('/api/strategies', strategy);
    return response.data;
  }

  async updateStrategy(id: string, updates: Partial<Strategy>): Promise<ApiResponse<Strategy>> {
    const response = await api.put(`/api/strategies/${id}`, updates);
    return response.data;
  }

  async deleteStrategy(id: string): Promise<ApiResponse<void>> {
    const response = await api.delete(`/api/strategies/${id}`);
    return response.data;
  }

  async startStrategy(id: string): Promise<ApiResponse<Strategy>> {
    const response = await api.post(`/api/strategies/${id}/start`);
    return response.data;
  }

  async stopStrategy(id: string): Promise<ApiResponse<Strategy>> {
    const response = await api.post(`/api/strategies/${id}/stop`);
    return response.data;
  }

  async pauseStrategy(id: string): Promise<ApiResponse<Strategy>> {
    const response = await api.post(`/api/strategies/${id}/pause`);
    return response.data;
  }

  // Portfolio APIs
  async getPortfolio(): Promise<ApiResponse<Portfolio>> {
    const response = await api.get('/api/portfolio');
    return response.data;
  }

  async getHoldings(): Promise<ApiResponse<Holding[]>> {
    const response = await api.get('/api/portfolio/holdings');
    return response.data;
  }

  // Backtest APIs
  async runBacktest(
    strategyId: string,
    symbol: string,
    startDate: string,
    endDate: string,
    initialCapital: number
  ): Promise<ApiResponse<BacktestResult>> {
    const response = await api.post('/api/backtest/run', {
      strategyId,
      symbol,
      startDate,
      endDate,
      initialCapital
    });
    return response.data;
  }

  async getBacktestResults(strategyId?: string): Promise<ApiResponse<BacktestResult[]>> {
    const url = strategyId ? `/api/backtest/results/${strategyId}` : '/api/backtest/results';
    const response = await api.get(url);
    return response.data;
  }

  async getBacktestResult(id: string): Promise<ApiResponse<BacktestResult>> {
    const response = await api.get(`/api/backtest/result/${id}`);
    return response.data;
  }

  // Market Data APIs
  async getCurrentPrice(symbol: string): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/market-data/current/${symbol}`);
    return response.data;
  }

  async getCurrentPrices(symbols: string[]): Promise<ApiResponse<any[]>> {
    const response = await api.post('/api/market-data/current', { symbols });
    return response.data;
  }

  async getHistoricalData(
    symbol: string,
    startDate: string,
    endDate: string,
    interval: string = '1d',
    source: string = 'api'
  ): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/api/market-data/historical/${symbol}`, {
      params: { startDate, endDate, interval, source }
    });
    return response.data;
  }

  async searchSymbols(query: string): Promise<ApiResponse<any[]>> {
    const response = await api.get('/api/market-data/search', { params: { q: query } });
    return response.data;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
